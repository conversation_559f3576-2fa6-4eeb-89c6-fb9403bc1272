import { useEffect } from 'react';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useFonts } from 'expo-font';
import * as SplashScreen from 'expo-splash-screen';
import { QueryClient, QueryClientProvider } from 'react-query';
import { PaperProvider, MD3DarkTheme } from 'react-native-paper';
import Toast from 'react-native-toast-message';
import { AuthProvider } from '../lib/auth-context';
import { DatabaseProvider } from '../lib/database-context';
import { NetworkProvider } from '../lib/network-context';

// Prevent the splash screen from auto-hiding
SplashScreen.preventAutoHideAsync();

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 60 * 1000, // 1 minute
      cacheTime: 5 * 60 * 1000, // 5 minutes
      retry: (failureCount, error: any) => {
        if (error?.response?.status >= 400 && error?.response?.status < 500) {
          return false;
        }
        return failureCount < 3;
      },
    },
    mutations: {
      retry: false,
    },
  },
});

// Custom theme for React Native Paper
const theme = {
  ...MD3DarkTheme,
  colors: {
    ...MD3DarkTheme.colors,
    primary: '#00f5ff', // neon-blue
    secondary: '#bf00ff', // neon-purple
    tertiary: '#39ff14', // neon-green
    surface: '#1e293b', // dark-800
    surfaceVariant: '#334155', // dark-700
    background: '#0f172a', // dark-950
    onBackground: '#ffffff',
    onSurface: '#ffffff',
  },
};

export default function RootLayout() {
  const [fontsLoaded] = useFonts({
    'Inter-Regular': require('../assets/fonts/Inter-Regular.ttf'),
    'Inter-Medium': require('../assets/fonts/Inter-Medium.ttf'),
    'Inter-SemiBold': require('../assets/fonts/Inter-SemiBold.ttf'),
    'Inter-Bold': require('../assets/fonts/Inter-Bold.ttf'),
    'JetBrainsMono-Regular': require('../assets/fonts/JetBrainsMono-Regular.ttf'),
    'JetBrainsMono-Bold': require('../assets/fonts/JetBrainsMono-Bold.ttf'),
  });

  useEffect(() => {
    if (fontsLoaded) {
      SplashScreen.hideAsync();
    }
  }, [fontsLoaded]);

  if (!fontsLoaded) {
    return null;
  }

  return (
    <QueryClientProvider client={queryClient}>
      <PaperProvider theme={theme}>
        <NetworkProvider>
          <DatabaseProvider>
            <AuthProvider>
              <StatusBar style="light" backgroundColor="#0f172a" />
              <Stack
                screenOptions={{
                  headerStyle: {
                    backgroundColor: '#1e293b',
                  },
                  headerTintColor: '#ffffff',
                  headerTitleStyle: {
                    fontFamily: 'Inter-SemiBold',
                  },
                  contentStyle: {
                    backgroundColor: '#0f172a',
                  },
                }}
              >
                <Stack.Screen 
                  name="index" 
                  options={{ 
                    headerShown: false 
                  }} 
                />
                <Stack.Screen 
                  name="auth" 
                  options={{ 
                    headerShown: false 
                  }} 
                />
                <Stack.Screen 
                  name="(tabs)" 
                  options={{ 
                    headerShown: false 
                  }} 
                />
                <Stack.Screen 
                  name="scan/[id]" 
                  options={{ 
                    title: 'Scan Result',
                    presentation: 'modal'
                  }} 
                />
                <Stack.Screen 
                  name="tools/[tool]" 
                  options={{ 
                    title: 'Security Tools',
                    presentation: 'modal'
                  }} 
                />
              </Stack>
              <Toast />
            </AuthProvider>
          </DatabaseProvider>
        </NetworkProvider>
      </PaperProvider>
    </QueryClientProvider>
  );
}
