{"name": "kodexguard", "version": "1.0.0", "description": "Platform mandiri cybersecurity & bug hunting Indonesia", "private": true, "workspaces": ["web", "api", "mobile", "shared"], "scripts": {"dev": "concurrently \"bun run dev:api\" \"bun run dev:web\"", "dev:web": "cd web && bun run dev", "dev:api": "cd api && bun run dev", "dev:mobile": "cd mobile && expo start", "dev:all": "concurrently \"bun run dev:api\" \"bun run dev:web\" \"bun run dev:mobile\"", "build": "bun run build:shared && bun run build:api && bun run build:web", "build:web": "cd web && bun run build", "build:api": "cd api && bun run build", "build:mobile": "cd mobile && expo build", "build:shared": "cd shared && bun run build", "test": "bun run test:api && bun run test:web", "test:api": "cd api && bun test", "test:web": "cd web && bun test", "lint": "bun run lint:web && bun run lint:api", "lint:web": "cd web && next lint", "lint:api": "cd api && eslint .", "db:setup": "cd api && bun run db:migrate && bun run db:seed", "db:migrate": "cd api && bun run migrate", "db:seed": "cd api && bun run seed", "clean": "rm -rf node_modules web/node_modules api/node_modules mobile/node_modules shared/node_modules", "install:all": "bun install && cd web && bun install && cd ../api && bun install && cd ../mobile && bun install && cd ../shared && bun install"}, "devDependencies": {"concurrently": "^8.2.2", "@types/node": "^20.10.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0", "bun": ">=1.0.0"}, "keywords": ["cybersecurity", "bug-hunting", "osint", "vulnerability-scanner", "penetration-testing", "security-tools", "indonesia"], "author": "KodeXGuard Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/kodexguard/kodexguard.git"}, "bugs": {"url": "https://github.com/kodexguard/kodexguard/issues"}, "homepage": "https://kodexguard.com"}