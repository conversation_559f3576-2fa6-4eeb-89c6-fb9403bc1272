'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { 
  ShieldCheckIcon, 
  MagnifyingGlassIcon, 
  BugAntIcon, 
  DocumentMagnifyingGlassIcon,
  RocketLaunchIcon,
  UsersIcon,
  ChartBarIcon,
  CpuChipIcon
} from '@heroicons/react/24/outline';

const features = [
  {
    name: 'OSINT Investigator',
    description: 'Pencarian mendalam untuk nama, NIK, NPWP, nomor HP, IMEI, email, dan domain',
    icon: MagnifyingGlassIcon,
    color: 'text-neon-blue',
    bgColor: 'bg-neon-blue/10',
  },
  {
    name: 'Vulnerability Scanner',
    description: 'Deteksi SQLi, XSS, LFI, RCE, Path Traversal, CSRF dengan mapping CVE/CVSS',
    icon: ShieldCheckIcon,
    color: 'text-neon-purple',
    bgColor: 'bg-neon-purple/10',
  },
  {
    name: 'File Analyzer',
    description: '<PERSON><PERSON><PERSON> webshell, ransomware, DDoS script, dan deteksi secret/token/API key',
    icon: DocumentMagnifyingGlassIcon,
    color: 'text-neon-green',
    bgColor: 'bg-neon-green/10',
  },
  {
    name: 'Bot Automation',
    description: 'Integrasi WhatsApp & Telegram untuk notifikasi dan kontrol via chat',
    icon: CpuChipIcon,
    color: 'text-neon-orange',
    bgColor: 'bg-neon-orange/10',
  },
  {
    name: 'CVE Intelligence',
    description: 'Database CVE terbaru dengan exploit tools dan informasi keamanan harian',
    icon: BugAntIcon,
    color: 'text-red-400',
    bgColor: 'bg-red-400/10',
  },
  {
    name: 'Komunitas Hunter',
    description: 'Leaderboard nasional bug hunter Indonesia dengan sistem poin dan achievement',
    icon: UsersIcon,
    color: 'text-yellow-400',
    bgColor: 'bg-yellow-400/10',
  },
];

const stats = [
  { name: 'Active Users', value: '2,500+', icon: UsersIcon },
  { name: 'Vulnerabilities Found', value: '15,000+', icon: BugAntIcon },
  { name: 'OSINT Queries', value: '50,000+', icon: MagnifyingGlassIcon },
  { name: 'Files Analyzed', value: '8,000+', icon: DocumentMagnifyingGlassIcon },
];

export default function HomePage() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  return (
    <div className="min-h-screen">
      {/* Navigation */}
      <nav className="relative z-50 bg-dark-900/50 backdrop-blur-md border-b border-dark-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-br from-neon-blue to-neon-purple rounded-lg flex items-center justify-center">
                  <ShieldCheckIcon className="w-5 h-5 text-white" />
                </div>
                <span className="text-xl font-display font-bold gradient-text">
                  KodeXGuard
                </span>
              </Link>
            </div>
            
            <div className="hidden md:flex items-center space-x-8">
              <Link href="/features" className="text-gray-300 hover:text-neon-blue transition-colors">
                Features
              </Link>
              <Link href="/pricing" className="text-gray-300 hover:text-neon-blue transition-colors">
                Pricing
              </Link>
              <Link href="/docs" className="text-gray-300 hover:text-neon-blue transition-colors">
                Docs
              </Link>
              <Link href="/community" className="text-gray-300 hover:text-neon-blue transition-colors">
                Community
              </Link>
            </div>

            <div className="flex items-center space-x-4">
              <Link 
                href="/auth/login" 
                className="text-gray-300 hover:text-white transition-colors"
              >
                Login
              </Link>
              <Link 
                href="/auth/register" 
                className="btn-neon"
              >
                Get Started
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-display font-bold mb-6">
              <span className="gradient-text">KodeXGuard</span>
              <br />
              <span className="text-white">Cybersecurity Platform</span>
            </h1>
            
            <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto">
              Platform mandiri cybersecurity & bug hunting Indonesia dengan fitur OSINT, 
              Vulnerability Scanner, Bot Automation, File Analyzer, dan komunitas hunter.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link href="/auth/register" className="btn-neon text-lg px-8 py-4">
                <RocketLaunchIcon className="w-5 h-5 mr-2" />
                Start Hunting
              </Link>
              <Link 
                href="/demo" 
                className="btn-neon-purple text-lg px-8 py-4"
              >
                <ChartBarIcon className="w-5 h-5 mr-2" />
                View Demo
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-dark-800/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.name}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center"
              >
                <div className="flex justify-center mb-4">
                  <div className="w-12 h-12 bg-neon-blue/20 rounded-lg flex items-center justify-center">
                    <stat.icon className="w-6 h-6 text-neon-blue" />
                  </div>
                </div>
                <div className="text-3xl font-bold text-white mb-2">{stat.value}</div>
                <div className="text-gray-400">{stat.name}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-5xl font-display font-bold text-white mb-6">
              Fitur <span className="gradient-text">Unggulan</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Lengkapi arsenal cybersecurity Anda dengan tools profesional yang dirancang 
              khusus untuk bug hunter dan security researcher Indonesia.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={feature.name}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="card-cyber-glow p-6 hover-lift"
              >
                <div className={`w-12 h-12 ${feature.bgColor} rounded-lg flex items-center justify-center mb-4`}>
                  <feature.icon className={`w-6 h-6 ${feature.color}`} />
                </div>
                <h3 className="text-xl font-semibold text-white mb-3">
                  {feature.name}
                </h3>
                <p className="text-gray-400">
                  {feature.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-neon-blue/10 via-neon-purple/10 to-neon-green/10">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-3xl md:text-5xl font-display font-bold text-white mb-6">
              Siap Menjadi <span className="gradient-text">Elite Hunter</span>?
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              Bergabunglah dengan komunitas cybersecurity terbesar Indonesia. 
              Mulai hunting bug, tingkatkan skill, dan raih penghasilan dari keahlian Anda.
            </p>
            <Link href="/auth/register" className="btn-neon text-lg px-8 py-4">
              Mulai Sekarang - Gratis
            </Link>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-dark-900 border-t border-dark-700 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <Link href="/" className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-gradient-to-br from-neon-blue to-neon-purple rounded-lg flex items-center justify-center">
                  <ShieldCheckIcon className="w-5 h-5 text-white" />
                </div>
                <span className="text-xl font-display font-bold gradient-text">
                  KodeXGuard
                </span>
              </Link>
              <p className="text-gray-400 mb-4 max-w-md">
                Platform mandiri cybersecurity & bug hunting Indonesia. 
                Empowering Indonesian Cybersecurity Community.
              </p>
              <div className="text-sm text-gray-500">
                © 2024 KodeXGuard. All rights reserved.
              </div>
            </div>
            
            <div>
              <h3 className="text-white font-semibold mb-4">Platform</h3>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/features" className="hover:text-neon-blue transition-colors">Features</Link></li>
                <li><Link href="/pricing" className="hover:text-neon-blue transition-colors">Pricing</Link></li>
                <li><Link href="/api" className="hover:text-neon-blue transition-colors">API</Link></li>
                <li><Link href="/docs" className="hover:text-neon-blue transition-colors">Documentation</Link></li>
              </ul>
            </div>
            
            <div>
              <h3 className="text-white font-semibold mb-4">Community</h3>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/leaderboard" className="hover:text-neon-blue transition-colors">Leaderboard</Link></li>
                <li><Link href="/discord" className="hover:text-neon-blue transition-colors">Discord</Link></li>
                <li><Link href="/telegram" className="hover:text-neon-blue transition-colors">Telegram</Link></li>
                <li><Link href="/blog" className="hover:text-neon-blue transition-colors">Blog</Link></li>
              </ul>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
