{"name": "@kodexguard/shared", "version": "1.0.0", "description": "Shared types, utilities, and constants for KodeXGuard", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "test": "bun test"}, "dependencies": {"zod": "^3.22.4"}, "devDependencies": {"typescript": "^5.3.0", "@types/node": "^20.10.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./types": {"types": "./dist/types/index.d.ts", "default": "./dist/types/index.js"}, "./utils": {"types": "./dist/utils/index.d.ts", "default": "./dist/utils/index.js"}, "./constants": {"types": "./dist/constants/index.d.ts", "default": "./dist/constants/index.js"}}}