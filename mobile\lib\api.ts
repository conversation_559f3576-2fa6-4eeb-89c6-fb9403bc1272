import axios from 'axios';
import * as SecureStore from 'expo-secure-store';
import Toast from 'react-native-toast-message';

const API_BASE_URL = __DEV__ 
  ? 'http://localhost:3001' 
  : 'https://api.kodexguard.com';

// Create axios instance
export const api = axios.create({
  baseURL: `${API_BASE_URL}/api/v1`,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  async (config) => {
    try {
      const token = await SecureStore.getItemAsync('auth-token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    } catch (error) {
      console.error('Error getting auth token:', error);
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    if (error.response) {
      const { status, data } = error.response;
      
      switch (status) {
        case 401:
          // Unauthorized - remove token
          await SecureStore.deleteItemAsync('auth-token');
          Toast.show({
            type: 'error',
            text1: 'Sesi Berakhir',
            text2: 'Silakan login kembali',
          });
          break;
        case 403:
          Toast.show({
            type: 'error',
            text1: 'Akses Ditolak',
            text2: 'Anda tidak memiliki izin untuk melakukan aksi ini',
          });
          break;
        case 404:
          Toast.show({
            type: 'error',
            text1: 'Tidak Ditemukan',
            text2: 'Resource yang diminta tidak ditemukan',
          });
          break;
        case 429:
          Toast.show({
            type: 'error',
            text1: 'Terlalu Banyak Permintaan',
            text2: 'Silakan coba lagi nanti',
          });
          break;
        case 500:
          Toast.show({
            type: 'error',
            text1: 'Server Error',
            text2: 'Terjadi kesalahan server',
          });
          break;
        default:
          if (data?.error) {
            Toast.show({
              type: 'error',
              text1: 'Error',
              text2: data.error,
            });
          }
      }
    } else if (error.request) {
      Toast.show({
        type: 'error',
        text1: 'Koneksi Gagal',
        text2: 'Periksa koneksi internet Anda',
      });
    }
    
    return Promise.reject(error);
  }
);

// API endpoints
export const authAPI = {
  login: (data: { identifier: string; password: string }) =>
    api.post('/auth/login', data),
  register: (data: { username: string; email: string; password: string; fullName: string }) =>
    api.post('/auth/register', data),
  verify: () => api.get('/auth/verify'),
  forgotPassword: (email: string) =>
    api.post('/auth/forgot-password', { email }),
};

export const userAPI = {
  getProfile: () => api.get('/users/profile'),
  updateProfile: (data: any) => api.put('/users/profile', data),
  getApiKeys: () => api.get('/users/api-keys'),
  createApiKey: (data: { name: string; rateLimit?: number }) =>
    api.post('/users/api-keys', data),
  getStats: () => api.get('/users/stats'),
};

export const osintAPI = {
  search: (data: { type: string; query: string; options?: any }) =>
    api.post('/osint/search', data),
  getResult: (id: string) => api.get(`/osint/search/${id}`),
  getHistory: (params?: any) => api.get('/osint/history', { params }),
};

export const vulnAPI = {
  startScan: (data: { target: string; scanTypes: string[]; options?: any }) =>
    api.post('/vulnerability/scan', data),
  getScanResult: (id: string) => api.get(`/vulnerability/scan/${id}`),
  getHistory: (params?: any) => api.get('/vulnerability/history', { params }),
  getScanTypes: () => api.get('/vulnerability/scan-types'),
};

export const fileAPI = {
  analyzeFile: (data: { fileName: string; fileContent: string; fileType: string }) =>
    api.post('/file-analysis/analyze', data),
  getResult: (id: string) => api.get(`/file-analysis/analyze/${id}`),
  getHistory: (params?: any) => api.get('/file-analysis/history', { params }),
};

export const toolsAPI = {
  hash: (data: { data: string; algorithm?: string }) =>
    api.post('/tools/hash', data),
  encode: (data: { data: string; encoding: string }) =>
    api.post('/tools/encode', data),
  decode: (data: { data: string; encoding: string }) =>
    api.post('/tools/decode', data),
  generatePayload: (data: { type: string; target?: string }) =>
    api.post('/tools/payload', data),
  generatePassword: (data: any) =>
    api.post('/tools/password-generator', data),
};

export const cveAPI = {
  getCVEs: (params?: any) => api.get('/cve', { params }),
  getCVEDetails: (cveId: string) => api.get(`/cve/${cveId}`),
  getRecent: (limit?: number) => api.get('/cve/recent/list', { params: { limit } }),
  search: (data: { query: string; limit?: string }) =>
    api.post('/cve/search', data),
};

export const dorkAPI = {
  getPresets: (params?: any) => api.get('/dorking/presets', { params }),
  getCategories: () => api.get('/dorking/categories'),
  getPopular: (limit?: number) => api.get('/dorking/popular', { params: { limit } }),
  search: (data: { query: string; limit?: number }) =>
    api.post('/dorking/search', data),
  getDaily: () => api.get('/dorking/daily'),
};

export const leaderboardAPI = {
  getTop: (limit?: number) => api.get('/leaderboard/top', { params: { limit } }),
  getMyPosition: () => api.get('/leaderboard/my-position'),
};

export const planAPI = {
  getPlans: () => api.get('/plans'),
  getCurrentPlan: () => api.get('/plans/current'),
  purchasePlan: (data: { planId: string; gateway: string; duration?: string }) =>
    api.post('/plans/purchase', data),
};

export default api;
