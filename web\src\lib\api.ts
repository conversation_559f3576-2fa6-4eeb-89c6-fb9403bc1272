import axios from 'axios';
import Cookies from 'js-cookie';
import toast from 'react-hot-toast';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

// Create axios instance
export const api = axios.create({
  baseURL: `${API_BASE_URL}/api/v1`,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = Cookies.get('auth-token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response) {
      const { status, data } = error.response;
      
      switch (status) {
        case 401:
          // Unauthorized - remove token and redirect to login
          Cookies.remove('auth-token');
          if (typeof window !== 'undefined') {
            window.location.href = '/auth/login';
          }
          break;
        case 403:
          toast.error('Akses ditolak. Anda tidak memiliki izin untuk melakukan aksi ini.');
          break;
        case 404:
          toast.error('Resource tidak ditemukan.');
          break;
        case 429:
          toast.error('Terlalu banyak permintaan. Silakan coba lagi nanti.');
          break;
        case 500:
          toast.error('Terjadi kesalahan server. Silakan coba lagi nanti.');
          break;
        default:
          if (data?.error) {
            toast.error(data.error);
          } else {
            toast.error('Terjadi kesalahan yang tidak diketahui.');
          }
      }
    } else if (error.request) {
      toast.error('Tidak dapat terhubung ke server. Periksa koneksi internet Anda.');
    } else {
      toast.error('Terjadi kesalahan yang tidak diketahui.');
    }
    
    return Promise.reject(error);
  }
);

// API endpoints
export const authAPI = {
  login: (data: { identifier: string; password: string }) =>
    api.post('/auth/login', data),
  register: (data: { username: string; email: string; password: string; fullName: string }) =>
    api.post('/auth/register', data),
  verify: () => api.get('/auth/verify'),
  forgotPassword: (email: string) =>
    api.post('/auth/forgot-password', { email }),
  resetPassword: (data: { token: string; password: string }) =>
    api.post('/auth/reset-password', data),
};

export const userAPI = {
  getProfile: () => api.get('/users/profile'),
  updateProfile: (data: any) => api.put('/users/profile', data),
  getApiKeys: () => api.get('/users/api-keys'),
  createApiKey: (data: { name: string; rateLimit?: number; expiresAt?: string }) =>
    api.post('/users/api-keys', data),
  updateApiKey: (id: string, data: any) => api.put(`/users/api-keys/${id}`, data),
  deleteApiKey: (id: string) => api.delete(`/users/api-keys/${id}`),
  getStats: () => api.get('/users/stats'),
};

export const osintAPI = {
  search: (data: { type: string; query: string; options?: any }) =>
    api.post('/osint/search', data),
  getResult: (id: string) => api.get(`/osint/search/${id}`),
  getHistory: (params?: any) => api.get('/osint/history', { params }),
  getSources: () => api.get('/osint/sources'),
};

export const vulnAPI = {
  startScan: (data: { target: string; scanTypes: string[]; options?: any }) =>
    api.post('/vulnerability/scan', data),
  getScanResult: (id: string) => api.get(`/vulnerability/scan/${id}`),
  getScanHistory: (params?: any) => api.get('/vulnerability/history', { params }),
  getScanTypes: () => api.get('/vulnerability/scan-types'),
  getStats: () => api.get('/vulnerability/stats'),
};

export const fileAPI = {
  analyzeFile: (data: { fileName: string; fileContent: string; fileType: string }) =>
    api.post('/file-analysis/analyze', data),
  getAnalysisResult: (id: string) => api.get(`/file-analysis/analyze/${id}`),
  getAnalysisHistory: (params?: any) => api.get('/file-analysis/history', { params }),
  getSignatures: () => api.get('/file-analysis/signatures'),
};

export const toolsAPI = {
  hash: (data: { data: string; algorithm?: string }) =>
    api.post('/tools/hash', data),
  encode: (data: { data: string; encoding: string }) =>
    api.post('/tools/encode', data),
  decode: (data: { data: string; encoding: string }) =>
    api.post('/tools/decode', data),
  generatePayload: (data: { type: string; target?: string; custom?: boolean }) =>
    api.post('/tools/payload', data),
  analyzeUrl: (data: { url: string }) =>
    api.post('/tools/url-analysis', data),
  analyzeText: (data: { text: string }) =>
    api.post('/tools/text-analysis', data),
  generatePassword: (data: any) =>
    api.post('/tools/password-generator', data),
};

export const cveAPI = {
  getCVEs: (params?: any) => api.get('/cve', { params }),
  getCVEDetails: (cveId: string) => api.get(`/cve/${cveId}`),
  getStats: () => api.get('/cve/stats/overview'),
  getRecent: (limit?: number) => api.get('/cve/recent/list', { params: { limit } }),
  getCritical: (limit?: number) => api.get('/cve/critical/list', { params: { limit } }),
  getWithExploits: (limit?: number) => api.get('/cve/exploits/available', { params: { limit } }),
  search: (data: { query: string; limit?: string }) =>
    api.post('/cve/search', data),
  getTrends: (year?: number) => api.get('/cve/trends/monthly', { params: { year } }),
};

export const dorkAPI = {
  getPresets: (params?: any) => api.get('/dorking/presets', { params }),
  getCategories: () => api.get('/dorking/categories'),
  getPopular: (limit?: number) => api.get('/dorking/popular', { params: { limit } }),
  getPreset: (id: string) => api.get(`/dorking/presets/${id}`),
  createPreset: (data: any) => api.post('/dorking/presets', data),
  getMyDorks: (params?: any) => api.get('/dorking/my-dorks', { params }),
  updatePreset: (id: string, data: any) => api.put(`/dorking/presets/${id}`, data),
  deletePreset: (id: string) => api.delete(`/dorking/presets/${id}`),
  search: (data: { query: string; limit?: number }) =>
    api.post('/dorking/search', data),
  getStats: () => api.get('/dorking/stats'),
  getDaily: () => api.get('/dorking/daily'),
};

export const leaderboardAPI = {
  getLeaderboard: (params?: any) => api.get('/leaderboard', { params }),
  getTop: (limit?: number) => api.get('/leaderboard/top', { params: { limit } }),
  getMyPosition: () => api.get('/leaderboard/my-position'),
  getStats: () => api.get('/leaderboard/stats'),
  getAchievements: (userId: string) => api.get(`/leaderboard/achievements/${userId}`),
  getMonthly: (year: number, month: number) =>
    api.get(`/leaderboard/monthly/${year}/${month}`),
  compareUsers: (userIds: string[]) =>
    api.post('/leaderboard/compare', { userIds }),
};

export const planAPI = {
  getPlans: () => api.get('/plans'),
  getPlan: (id: string) => api.get(`/plans/${id}`),
  purchasePlan: (data: { planId: string; gateway: string; duration?: string }) =>
    api.post('/plans/purchase', data),
  getPaymentStatus: (id: string) => api.get(`/plans/payment/${id}`),
  getPaymentHistory: (params?: any) => api.get('/plans/payments/history', { params }),
  getCurrentPlan: () => api.get('/plans/current'),
  comparePlans: () => api.get('/plans/compare'),
  changePlan: (data: { newPlanType: string }) =>
    api.post('/plans/change', data),
};

export const botAPI = {
  getStatus: () => api.get('/bots/status'),
  getBots: () => api.get('/bots'),
  createBot: (data: any) => api.post('/bots', data),
  updateBot: (id: string, data: any) => api.put(`/bots/${id}`, data),
  generateQR: (id: string) => api.post(`/bots/${id}/qr`),
  deleteBot: (id: string) => api.delete(`/bots/${id}`),
  getCommands: () => api.get('/bots/commands'),
  sendMessage: (data: { botId: string; recipient: string; message: string }) =>
    api.post('/bots/send', data),
  getStats: () => api.get('/bots/stats'),
};

export const adminAPI = {
  getDashboard: () => api.get('/admin/dashboard'),
  getUsers: (params?: any) => api.get('/admin/users', { params }),
  updateUser: (id: string, data: any) => api.put(`/admin/users/${id}`, data),
  deleteUser: (id: string) => api.delete(`/admin/users/${id}`),
  getSettings: () => api.get('/admin/settings'),
  updateSettings: (data: any) => api.put('/admin/settings', data),
  getAuditLogs: (params?: any) => api.get('/admin/audit-logs', { params }),
  getPayments: (params?: any) => api.get('/admin/payments', { params }),
  updatePayment: (id: string, data: any) => api.put(`/admin/payments/${id}`, data),
};

export default api;
