{"name": "@kodexguard/mobile", "version": "1.0.0", "description": "KodeXGuard Mobile App", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "build:all": "eas build --platform all", "submit:android": "eas submit --platform android", "submit:ios": "eas submit --platform ios", "update": "eas update", "lint": "eslint .", "type-check": "tsc --noEmit"}, "dependencies": {"@kodexguard/shared": "workspace:*", "expo": "~50.0.0", "expo-router": "~3.4.0", "expo-status-bar": "~1.11.1", "expo-constants": "~15.4.0", "expo-linking": "~6.2.2", "expo-font": "~11.10.0", "expo-splash-screen": "~0.26.0", "expo-system-ui": "~2.9.0", "expo-web-browser": "~12.8.0", "expo-secure-store": "~12.8.0", "expo-sqlite": "~13.4.0", "expo-file-system": "~16.0.0", "expo-document-picker": "~11.10.0", "expo-clipboard": "~5.0.0", "expo-haptics": "~12.8.0", "expo-network": "~5.8.0", "react": "18.2.0", "react-native": "0.73.0", "react-native-safe-area-context": "4.8.2", "react-native-screens": "~3.29.0", "react-native-gesture-handler": "~2.14.0", "react-native-reanimated": "~3.6.2", "react-native-svg": "14.1.0", "react-native-vector-icons": "^10.0.3", "@react-native-async-storage/async-storage": "1.21.0", "@react-native-community/netinfo": "11.1.0", "react-query": "^3.39.3", "axios": "^1.6.2", "react-hook-form": "^7.48.2", "date-fns": "^2.30.0", "react-native-toast-message": "^2.1.7", "react-native-modal": "^13.0.1", "react-native-paper": "^5.11.6", "react-native-chart-kit": "^6.12.0", "react-native-syntax-highlighter": "^2.1.0"}, "devDependencies": {"@babel/core": "^7.23.6", "@types/react": "~18.2.45", "@types/react-native": "~0.73.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "eslint-config-expo": "^7.0.0", "typescript": "^5.3.0"}, "private": true}