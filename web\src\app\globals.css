@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Import fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@100;200;300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&display=swap');

/* Base styles */
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

a {
  color: inherit;
  text-decoration: none;
}

/* Dark mode variables */
:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-dark-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-primary-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-primary-500;
}

/* Cyberpunk glow effects */
.glow-text {
  text-shadow: 0 0 10px currentColor;
}

.glow-border {
  box-shadow: 0 0 10px currentColor;
}

.cyber-grid {
  background-image: 
    linear-gradient(rgba(0,245,255,0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0,245,255,0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Neon button styles */
.btn-neon {
  @apply relative px-6 py-3 font-semibold text-white bg-transparent border-2 border-neon-blue rounded-lg;
  @apply hover:bg-neon-blue hover:text-dark-900 transition-all duration-300;
  @apply before:absolute before:inset-0 before:bg-neon-blue before:opacity-0 before:transition-opacity before:duration-300;
  @apply hover:before:opacity-20 hover:shadow-neon;
}

.btn-neon-purple {
  @apply border-neon-purple hover:bg-neon-purple hover:shadow-neon-purple;
}

.btn-neon-green {
  @apply border-neon-green hover:bg-neon-green hover:shadow-neon-green;
}

/* Card styles */
.card-cyber {
  @apply bg-dark-800/50 backdrop-blur-sm border border-dark-600 rounded-lg;
  @apply shadow-cyber hover:shadow-cyber-lg transition-all duration-300;
}

.card-cyber-glow {
  @apply card-cyber border-neon-blue/30 hover:border-neon-blue/60;
  @apply hover:shadow-neon/20;
}

/* Input styles */
.input-cyber {
  @apply bg-dark-800/50 border border-dark-600 rounded-lg px-4 py-2;
  @apply text-white placeholder-dark-400 focus:border-neon-blue focus:ring-1 focus:ring-neon-blue;
  @apply transition-all duration-300;
}

/* Loading animations */
.loading-dots {
  @apply inline-flex space-x-1;
}

.loading-dots > div {
  @apply w-2 h-2 bg-current rounded-full animate-pulse;
  animation-delay: calc(var(--i) * 0.2s);
}

/* Code syntax highlighting */
.code-block {
  @apply bg-dark-900 border border-dark-700 rounded-lg p-4 font-mono text-sm;
  @apply overflow-x-auto;
}

/* Status indicators */
.status-online {
  @apply w-3 h-3 bg-neon-green rounded-full animate-pulse;
}

.status-offline {
  @apply w-3 h-3 bg-red-500 rounded-full;
}

.status-pending {
  @apply w-3 h-3 bg-yellow-500 rounded-full animate-pulse;
}

/* Severity badges */
.severity-critical {
  @apply bg-red-500/20 text-red-400 border border-red-500/30;
}

.severity-high {
  @apply bg-orange-500/20 text-orange-400 border border-orange-500/30;
}

.severity-medium {
  @apply bg-yellow-500/20 text-yellow-400 border border-yellow-500/30;
}

.severity-low {
  @apply bg-green-500/20 text-green-400 border border-green-500/30;
}

/* Plan badges */
.plan-free {
  @apply bg-gray-500/20 text-gray-400 border border-gray-500/30;
}

.plan-student {
  @apply bg-blue-500/20 text-blue-400 border border-blue-500/30;
}

.plan-hobby {
  @apply bg-purple-500/20 text-purple-400 border border-purple-500/30;
}

.plan-bughunter {
  @apply bg-orange-500/20 text-orange-400 border border-orange-500/30;
}

.plan-cybersecurity {
  @apply bg-red-500/20 text-red-400 border border-red-500/30;
}

/* Responsive utilities */
@media (max-width: 640px) {
  .mobile-hidden {
    display: none;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}

/* Focus styles for accessibility */
.focus-visible:focus-visible {
  @apply outline-none ring-2 ring-neon-blue ring-offset-2 ring-offset-dark-900;
}

/* Custom animations */
@keyframes matrix-rain {
  0% { transform: translateY(-100vh); }
  100% { transform: translateY(100vh); }
}

.matrix-rain {
  animation: matrix-rain 10s linear infinite;
}

/* Glassmorphism effect */
.glass {
  @apply bg-white/10 backdrop-blur-md border border-white/20;
}

.glass-dark {
  @apply bg-dark-900/30 backdrop-blur-md border border-dark-700/50;
}

/* Gradient text */
.gradient-text {
  @apply bg-gradient-to-r from-neon-blue via-neon-purple to-neon-green bg-clip-text text-transparent;
}

/* Hover effects */
.hover-lift {
  @apply transition-transform duration-300 hover:-translate-y-1;
}

.hover-glow {
  @apply transition-all duration-300 hover:shadow-neon/30;
}

/* Loading spinner */
.spinner {
  @apply w-6 h-6 border-2 border-current border-t-transparent rounded-full animate-spin;
}

/* Toast notifications */
.toast-success {
  @apply bg-green-500/90 text-white border border-green-400;
}

.toast-error {
  @apply bg-red-500/90 text-white border border-red-400;
}

.toast-warning {
  @apply bg-yellow-500/90 text-white border border-yellow-400;
}

.toast-info {
  @apply bg-blue-500/90 text-white border border-blue-400;
}
