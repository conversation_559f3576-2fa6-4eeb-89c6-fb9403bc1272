{"expo": {"name": "KodeXGuard", "slug": "kodexguard", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "dark", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#0f172a"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.kodexguard.mobile", "buildNumber": "1.0.0"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#0f172a"}, "package": "com.kodexguard.mobile", "versionCode": 1, "permissions": ["android.permission.INTERNET", "android.permission.ACCESS_NETWORK_STATE", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE"]}, "web": {"favicon": "./assets/favicon.png", "bundler": "metro"}, "plugins": ["expo-router", "expo-font", "expo-secure-store", "expo-sqlite"], "scheme": "kodexguard", "extra": {"router": {"origin": false}, "eas": {"projectId": "your-project-id"}}, "owner": "kodexguard"}}