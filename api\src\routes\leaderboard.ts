import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import { db } from '../database/connection';
import { leaderboard, users } from '../database/schema';
import { eq, desc, asc } from 'drizzle-orm';
import { asyncHandler, notFoundError } from '../middleware/error-handler';

const leaderboardRoutes = new Hono();

// Get leaderboard
const getLeaderboardSchema = z.object({
  page: z.string().optional(),
  limit: z.string().optional(),
  sortBy: z.enum(['score', 'totalScans', 'totalVulns', 'totalReports']).optional()
});

leaderboardRoutes.get('/', zValidator('query', getLeaderboardSchema), asyncHandler(async (c) => {
  const {
    page = '1',
    limit = '50',
    sortBy = 'score'
  } = c.req.valid('query');

  const pageNum = parseInt(page);
  const limitNum = parseInt(limit);
  const offset = (pageNum - 1) * limitNum;

  // Get leaderboard with user details
  const leaderboardData = await db
    .select({
      rank: leaderboard.rank,
      score: leaderboard.score,
      totalScans: leaderboard.totalScans,
      totalVulns: leaderboard.totalVulns,
      totalReports: leaderboard.totalReports,
      lastUpdated: leaderboard.lastUpdated,
      user: {
        id: users.id,
        username: users.username,
        fullName: users.fullName,
        avatar: users.avatar,
        plan: users.plan
      }
    })
    .from(leaderboard)
    .innerJoin(users, eq(leaderboard.userId, users.id))
    .where(eq(users.isActive, true))
    .orderBy(
      sortBy === 'score' ? desc(leaderboard.score) :
      sortBy === 'totalScans' ? desc(leaderboard.totalScans) :
      sortBy === 'totalVulns' ? desc(leaderboard.totalVulns) :
      desc(leaderboard.totalReports)
    )
    .limit(limitNum)
    .offset(offset);

  return c.json({
    success: true,
    data: leaderboardData,
    pagination: {
      page: pageNum,
      limit: limitNum,
      total: leaderboardData.length
    }
  });
}));

// Get top performers
leaderboardRoutes.get('/top', asyncHandler(async (c) => {
  const limit = parseInt(c.req.query('limit') || '10');

  const topPerformers = await db
    .select({
      rank: leaderboard.rank,
      score: leaderboard.score,
      totalScans: leaderboard.totalScans,
      totalVulns: leaderboard.totalVulns,
      totalReports: leaderboard.totalReports,
      user: {
        id: users.id,
        username: users.username,
        fullName: users.fullName,
        avatar: users.avatar,
        plan: users.plan
      }
    })
    .from(leaderboard)
    .innerJoin(users, eq(leaderboard.userId, users.id))
    .where(eq(users.isActive, true))
    .orderBy(desc(leaderboard.score))
    .limit(limit);

  return c.json({
    success: true,
    data: topPerformers
  });
}));

// Get user's leaderboard position
leaderboardRoutes.get('/my-position', asyncHandler(async (c) => {
  const user = c.get('user');

  const [userPosition] = await db
    .select({
      rank: leaderboard.rank,
      score: leaderboard.score,
      totalScans: leaderboard.totalScans,
      totalVulns: leaderboard.totalVulns,
      totalReports: leaderboard.totalReports,
      lastUpdated: leaderboard.lastUpdated
    })
    .from(leaderboard)
    .where(eq(leaderboard.userId, user.id))
    .limit(1);

  if (!userPosition) {
    // Create initial leaderboard entry for user
    await db.insert(leaderboard).values({
      id: user.id,
      userId: user.id,
      totalScans: 0,
      totalVulns: 0,
      totalReports: 0,
      score: 0,
      rank: 0
    });

    return c.json({
      success: true,
      data: {
        rank: 0,
        score: 0,
        totalScans: 0,
        totalVulns: 0,
        totalReports: 0,
        lastUpdated: new Date()
      }
    });
  }

  return c.json({
    success: true,
    data: userPosition
  });
}));

// Get leaderboard statistics
leaderboardRoutes.get('/stats', asyncHandler(async (c) => {
  const allEntries = await db
    .select({
      score: leaderboard.score,
      totalScans: leaderboard.totalScans,
      totalVulns: leaderboard.totalVulns,
      totalReports: leaderboard.totalReports
    })
    .from(leaderboard);

  const stats = {
    totalUsers: allEntries.length,
    totalScans: allEntries.reduce((sum, entry) => sum + entry.totalScans, 0),
    totalVulnerabilities: allEntries.reduce((sum, entry) => sum + entry.totalVulns, 0),
    totalReports: allEntries.reduce((sum, entry) => sum + entry.totalReports, 0),
    averageScore: allEntries.length > 0 
      ? allEntries.reduce((sum, entry) => sum + entry.score, 0) / allEntries.length 
      : 0,
    topScore: allEntries.length > 0 
      ? Math.max(...allEntries.map(entry => entry.score)) 
      : 0,
    scoreDistribution: {
      '0-100': allEntries.filter(e => e.score <= 100).length,
      '101-500': allEntries.filter(e => e.score > 100 && e.score <= 500).length,
      '501-1000': allEntries.filter(e => e.score > 500 && e.score <= 1000).length,
      '1000+': allEntries.filter(e => e.score > 1000).length
    }
  };

  return c.json({
    success: true,
    data: stats
  });
}));

// Get user achievements
leaderboardRoutes.get('/achievements/:userId', asyncHandler(async (c) => {
  const userId = c.req.param('userId');

  const [userStats] = await db
    .select()
    .from(leaderboard)
    .where(eq(leaderboard.userId, userId))
    .limit(1);

  if (!userStats) {
    throw notFoundError('User leaderboard entry');
  }

  // Calculate achievements based on stats
  const achievements = [];

  // Scan achievements
  if (userStats.totalScans >= 1) achievements.push({ name: 'First Scan', description: 'Completed your first scan', icon: '🔍' });
  if (userStats.totalScans >= 10) achievements.push({ name: 'Scanner', description: 'Completed 10 scans', icon: '🔎' });
  if (userStats.totalScans >= 100) achievements.push({ name: 'Scan Master', description: 'Completed 100 scans', icon: '🕵️' });
  if (userStats.totalScans >= 1000) achievements.push({ name: 'Scan Legend', description: 'Completed 1000 scans', icon: '🏆' });

  // Vulnerability achievements
  if (userStats.totalVulns >= 1) achievements.push({ name: 'Bug Hunter', description: 'Found your first vulnerability', icon: '🐛' });
  if (userStats.totalVulns >= 10) achievements.push({ name: 'Vulnerability Finder', description: 'Found 10 vulnerabilities', icon: '🔓' });
  if (userStats.totalVulns >= 50) achievements.push({ name: 'Security Expert', description: 'Found 50 vulnerabilities', icon: '🛡️' });
  if (userStats.totalVulns >= 100) achievements.push({ name: 'Elite Hacker', description: 'Found 100 vulnerabilities', icon: '💀' });

  // Score achievements
  if (userStats.score >= 100) achievements.push({ name: 'Rising Star', description: 'Reached 100 points', icon: '⭐' });
  if (userStats.score >= 500) achievements.push({ name: 'Skilled Hunter', description: 'Reached 500 points', icon: '🌟' });
  if (userStats.score >= 1000) achievements.push({ name: 'Expert Hunter', description: 'Reached 1000 points', icon: '💫' });
  if (userStats.score >= 5000) achievements.push({ name: 'Legendary Hunter', description: 'Reached 5000 points', icon: '🏅' });

  // Rank achievements
  if (userStats.rank <= 10 && userStats.rank > 0) achievements.push({ name: 'Top 10', description: 'Reached top 10 ranking', icon: '🥇' });
  if (userStats.rank <= 3 && userStats.rank > 0) achievements.push({ name: 'Podium Finish', description: 'Reached top 3 ranking', icon: '🏆' });
  if (userStats.rank === 1) achievements.push({ name: 'Champion', description: 'Reached #1 ranking', icon: '👑' });

  return c.json({
    success: true,
    data: {
      userId,
      achievements,
      stats: userStats
    }
  });
}));

// Get monthly leaderboard
leaderboardRoutes.get('/monthly/:year/:month', asyncHandler(async (c) => {
  const year = parseInt(c.req.param('year'));
  const month = parseInt(c.req.param('month'));
  const limit = parseInt(c.req.query('limit') || '50');

  // For now, return the same leaderboard (in real implementation, track monthly stats)
  const monthlyLeaderboard = await db
    .select({
      rank: leaderboard.rank,
      score: leaderboard.score,
      totalScans: leaderboard.totalScans,
      totalVulns: leaderboard.totalVulns,
      totalReports: leaderboard.totalReports,
      user: {
        id: users.id,
        username: users.username,
        fullName: users.fullName,
        avatar: users.avatar,
        plan: users.plan
      }
    })
    .from(leaderboard)
    .innerJoin(users, eq(leaderboard.userId, users.id))
    .where(eq(users.isActive, true))
    .orderBy(desc(leaderboard.score))
    .limit(limit);

  return c.json({
    success: true,
    data: {
      year,
      month,
      leaderboard: monthlyLeaderboard
    }
  });
}));

// Compare users
const compareUsersSchema = z.object({
  userIds: z.array(z.string()).min(2).max(5)
});

leaderboardRoutes.post('/compare', zValidator('json', compareUsersSchema), asyncHandler(async (c) => {
  const { userIds } = c.req.valid('json');

  const userComparison = await db
    .select({
      rank: leaderboard.rank,
      score: leaderboard.score,
      totalScans: leaderboard.totalScans,
      totalVulns: leaderboard.totalVulns,
      totalReports: leaderboard.totalReports,
      user: {
        id: users.id,
        username: users.username,
        fullName: users.fullName,
        avatar: users.avatar,
        plan: users.plan
      }
    })
    .from(leaderboard)
    .innerJoin(users, eq(leaderboard.userId, users.id))
    .where(eq(users.isActive, true));

  const filteredUsers = userComparison.filter(entry => 
    userIds.includes(entry.user.id)
  );

  return c.json({
    success: true,
    data: {
      users: filteredUsers,
      comparison: {
        highestScore: Math.max(...filteredUsers.map(u => u.score)),
        mostScans: Math.max(...filteredUsers.map(u => u.totalScans)),
        mostVulns: Math.max(...filteredUsers.map(u => u.totalVulns)),
        bestRank: Math.min(...filteredUsers.map(u => u.rank).filter(r => r > 0))
      }
    }
  });
}));

export default leaderboardRoutes;
