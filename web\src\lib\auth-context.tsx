'use client';

import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import { api } from './api';
import Cookies from 'js-cookie';
import toast from 'react-hot-toast';

interface User {
  id: string;
  username: string;
  email: string;
  fullName: string;
  avatar?: string;
  role: string;
  plan: string;
  planExpiry?: string;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (identifier: string, password: string) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => void;
  updateUser: (data: Partial<User>) => void;
}

interface RegisterData {
  username: string;
  email: string;
  password: string;
  fullName: string;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    checkAuth();
  }, []);

  const checkAuth = async () => {
    try {
      const token = Cookies.get('auth-token');
      if (!token) {
        setLoading(false);
        return;
      }

      const response = await api.get('/auth/verify');
      if (response.data.success) {
        setUser(response.data.data.user);
      } else {
        Cookies.remove('auth-token');
      }
    } catch (error) {
      console.error('Auth check failed:', error);
      Cookies.remove('auth-token');
    } finally {
      setLoading(false);
    }
  };

  const login = async (identifier: string, password: string) => {
    try {
      const response = await api.post('/auth/login', {
        identifier,
        password,
      });

      if (response.data.success) {
        const { user, token } = response.data.data;
        
        // Store token in cookie
        Cookies.set('auth-token', token, { 
          expires: 7, // 7 days
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'strict'
        });

        setUser(user);
        toast.success('Login berhasil!');
        router.push('/dashboard');
      } else {
        throw new Error(response.data.error || 'Login gagal');
      }
    } catch (error: any) {
      const message = error.response?.data?.error || error.message || 'Login gagal';
      toast.error(message);
      throw error;
    }
  };

  const register = async (data: RegisterData) => {
    try {
      const response = await api.post('/auth/register', data);

      if (response.data.success) {
        const { user, token } = response.data.data;
        
        // Store token in cookie
        Cookies.set('auth-token', token, { 
          expires: 7,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'strict'
        });

        setUser(user);
        toast.success('Registrasi berhasil! Selamat datang di KodeXGuard!');
        router.push('/dashboard');
      } else {
        throw new Error(response.data.error || 'Registrasi gagal');
      }
    } catch (error: any) {
      const message = error.response?.data?.error || error.message || 'Registrasi gagal';
      toast.error(message);
      throw error;
    }
  };

  const logout = () => {
    Cookies.remove('auth-token');
    setUser(null);
    toast.success('Logout berhasil');
    router.push('/');
  };

  const updateUser = (data: Partial<User>) => {
    if (user) {
      setUser({ ...user, ...data });
    }
  };

  const value = {
    user,
    loading,
    login,
    register,
    logout,
    updateUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
