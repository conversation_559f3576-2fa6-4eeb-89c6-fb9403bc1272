import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import { db } from '../database/connection';
import { fileAnalysis } from '../database/schema';
import { eq } from 'drizzle-orm';
import { generateId, generateHash, ThreatType, SecretType } from '@kodexguard/shared';
import { asyncHandler, validationError, notFoundError } from '../middleware/error-handler';
import { planRateLimiter } from '../middleware/rate-limiter';

const fileRoutes = new Hono();

// Apply plan-based rate limiting
fileRoutes.use('*', planRateLimiter({
  free: 3,
  student: 15,
  hobby: 30,
  bughunter: 100,
  cybersecurity: -1
}));

// File upload schema
const fileUploadSchema = z.object({
  fileName: z.string().min(1).max(255),
  fileContent: z.string(), // Base64 encoded content
  fileType: z.string().max(100)
});

// Analyze file endpoint
fileRoutes.post('/analyze', zValidator('json', fileUploadSchema), asyncHandler(async (c) => {
  const user = c.get('user');
  const { fileName, fileContent, fileType } = c.req.valid('json');

  // Decode base64 content
  let decodedContent: Buffer;
  try {
    decodedContent = Buffer.from(fileContent, 'base64');
  } catch (error) {
    throw validationError('Invalid file content encoding');
  }

  // Check file size (10MB limit)
  if (decodedContent.length > 10 * 1024 * 1024) {
    throw validationError('File size exceeds 10MB limit');
  }

  // Generate file hashes
  const contentString = decodedContent.toString('utf8');
  const hashes = {
    md5: generateHash.md5(contentString),
    sha1: generateHash.sha1(contentString),
    sha256: generateHash.sha256(contentString),
    sha512: generateHash.sha512(contentString)
  };

  // Create analysis record
  const analysisId = generateId();
  await db.insert(fileAnalysis).values({
    id: analysisId,
    userId: user.id,
    fileName,
    fileType,
    fileSize: decodedContent.length,
    threats: [],
    secrets: [],
    hash: hashes,
    status: 'pending'
  });

  // Start file analysis (async)
  performFileAnalysis(analysisId, fileName, contentString, fileType);

  return c.json({
    success: true,
    message: 'File analysis started',
    data: {
      analysisId,
      fileName,
      fileType,
      fileSize: decodedContent.length,
      hashes,
      status: 'pending'
    }
  }, 202);
}));

// Get analysis result
fileRoutes.get('/analyze/:id', asyncHandler(async (c) => {
  const user = c.get('user');
  const analysisId = c.req.param('id');

  const [analysis] = await db
    .select()
    .from(fileAnalysis)
    .where(eq(fileAnalysis.id, analysisId))
    .limit(1);

  if (!analysis || analysis.userId !== user.id) {
    throw notFoundError('File analysis');
  }

  return c.json({
    success: true,
    data: analysis
  });
}));

// Get analysis history
fileRoutes.get('/history', asyncHandler(async (c) => {
  const user = c.get('user');
  const page = parseInt(c.req.query('page') || '1');
  const limit = parseInt(c.req.query('limit') || '20');
  const offset = (page - 1) * limit;

  const analyses = await db
    .select({
      id: fileAnalysis.id,
      fileName: fileAnalysis.fileName,
      fileType: fileAnalysis.fileType,
      fileSize: fileAnalysis.fileSize,
      status: fileAnalysis.status,
      createdAt: fileAnalysis.createdAt,
      completedAt: fileAnalysis.completedAt
    })
    .from(fileAnalysis)
    .where(eq(fileAnalysis.userId, user.id))
    .orderBy(fileAnalysis.createdAt)
    .limit(limit)
    .offset(offset);

  return c.json({
    success: true,
    data: analyses,
    pagination: {
      page,
      limit,
      total: analyses.length
    }
  });
}));

// Get threat signatures
fileRoutes.get('/signatures', asyncHandler(async (c) => {
  const signatures = {
    webshell: [
      'eval(',
      'base64_decode(',
      'system(',
      'exec(',
      'shell_exec(',
      'passthru(',
      'file_get_contents('
    ],
    ransomware: [
      'CryptEncrypt',
      'CryptDecrypt',
      'ransom',
      'encrypt_files',
      'decrypt_files'
    ],
    ddos_script: [
      'socket_create',
      'fsockopen',
      'curl_multi',
      'flood',
      'ddos'
    ],
    malware: [
      'CreateRemoteThread',
      'VirtualAlloc',
      'WriteProcessMemory',
      'SetWindowsHookEx'
    ]
  };

  return c.json({
    success: true,
    data: signatures
  });
}));

// Helper function to perform file analysis
async function performFileAnalysis(analysisId: string, fileName: string, content: string, fileType: string) {
  try {
    // Update status to analyzing
    await db
      .update(fileAnalysis)
      .set({ status: 'analyzing' })
      .where(eq(fileAnalysis.id, analysisId));

    // Analyze for threats
    const threats = await detectThreats(content, fileName, fileType);
    
    // Analyze for secrets
    const secrets = await detectSecrets(content);

    // Update with results
    await db
      .update(fileAnalysis)
      .set({
        threats,
        secrets,
        status: 'completed',
        completedAt: new Date()
      })
      .where(eq(fileAnalysis.id, analysisId));

  } catch (error) {
    console.error('File analysis error:', error);
    
    // Update status to failed
    await db
      .update(fileAnalysis)
      .set({
        status: 'failed',
        completedAt: new Date()
      })
      .where(eq(fileAnalysis.id, analysisId));
  }
}

async function detectThreats(content: string, fileName: string, fileType: string) {
  const threats = [];
  
  // Webshell detection
  const webshellSignatures = [
    'eval(',
    'base64_decode(',
    'system(',
    'exec(',
    'shell_exec(',
    'passthru(',
    'file_get_contents('
  ];

  for (const signature of webshellSignatures) {
    if (content.includes(signature)) {
      threats.push({
        type: ThreatType.WEBSHELL,
        severity: 'high',
        description: `Potential webshell detected: ${signature}`,
        location: `Line containing: ${signature}`,
        confidence: 85
      });
    }
  }

  // Ransomware detection
  const ransomwareSignatures = [
    'CryptEncrypt',
    'CryptDecrypt',
    'ransom',
    'encrypt_files',
    'decrypt_files'
  ];

  for (const signature of ransomwareSignatures) {
    if (content.toLowerCase().includes(signature.toLowerCase())) {
      threats.push({
        type: ThreatType.RANSOMWARE,
        severity: 'critical',
        description: `Potential ransomware detected: ${signature}`,
        location: `Content contains: ${signature}`,
        confidence: 90
      });
    }
  }

  // DDoS script detection
  const ddosSignatures = [
    'socket_create',
    'fsockopen',
    'curl_multi',
    'flood',
    'ddos'
  ];

  for (const signature of ddosSignatures) {
    if (content.toLowerCase().includes(signature.toLowerCase())) {
      threats.push({
        type: ThreatType.DDOS_SCRIPT,
        severity: 'medium',
        description: `Potential DDoS script detected: ${signature}`,
        location: `Content contains: ${signature}`,
        confidence: 75
      });
    }
  }

  return threats;
}

async function detectSecrets(content: string) {
  const secrets = [];
  
  // API key patterns
  const apiKeyPatterns = [
    /api[_-]?key[_-]?[=:]\s*['"]?([a-zA-Z0-9]{20,})/gi,
    /key[_-]?[=:]\s*['"]?([a-zA-Z0-9]{20,})/gi
  ];

  for (const pattern of apiKeyPatterns) {
    const matches = content.matchAll(pattern);
    for (const match of matches) {
      secrets.push({
        type: SecretType.API_KEY,
        value: match[1].substring(0, 8) + '...',
        location: `Character position: ${match.index}`,
        confidence: 80
      });
    }
  }

  // Password patterns
  const passwordPatterns = [
    /password[_-]?[=:]\s*['"]?([^\s'"]{8,})/gi,
    /pwd[_-]?[=:]\s*['"]?([^\s'"]{8,})/gi
  ];

  for (const pattern of passwordPatterns) {
    const matches = content.matchAll(pattern);
    for (const match of matches) {
      secrets.push({
        type: SecretType.PASSWORD,
        value: '***REDACTED***',
        location: `Character position: ${match.index}`,
        confidence: 85
      });
    }
  }

  // Token patterns
  const tokenPatterns = [
    /token[_-]?[=:]\s*['"]?([a-zA-Z0-9._-]{20,})/gi,
    /bearer[_-]?\s+([a-zA-Z0-9._-]{20,})/gi
  ];

  for (const pattern of tokenPatterns) {
    const matches = content.matchAll(pattern);
    for (const match of matches) {
      secrets.push({
        type: SecretType.TOKEN,
        value: match[1].substring(0, 8) + '...',
        location: `Character position: ${match.index}`,
        confidence: 90
      });
    }
  }

  // Database URL patterns
  const dbUrlPatterns = [
    /(mysql|postgresql|mongodb):\/\/[^\s'"]+/gi,
    /database[_-]?url[_-]?[=:]\s*['"]?([^\s'"]+)/gi
  ];

  for (const pattern of dbUrlPatterns) {
    const matches = content.matchAll(pattern);
    for (const match of matches) {
      secrets.push({
        type: SecretType.DATABASE_URL,
        value: '***REDACTED***',
        location: `Character position: ${match.index}`,
        confidence: 95
      });
    }
  }

  return secrets;
}

export default fileRoutes;
