import type { Metada<PERSON> } from 'next';
import { Inter, JetBrains_Mono, Orbitron } from 'next/font/google';
import './globals.css';
import { Providers } from '@/components/providers';
import { Toaster } from 'react-hot-toast';

const inter = Inter({ 
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
});

const jetbrainsMono = JetBrains_Mono({ 
  subsets: ['latin'],
  variable: '--font-jetbrains-mono',
  display: 'swap',
});

const orbitron = Orbitron({ 
  subsets: ['latin'],
  variable: '--font-orbitron',
  display: 'swap',
});

export const metadata: Metadata = {
  title: 'KodeXGuard - Cybersecurity & Bug Hunting Platform',
  description: 'Platform mandiri cybersecurity & bug hunting Indonesia dengan fitur OSINT, Vulnerability Scanner, Bot Automation, File Analyzer, dan komunitas hunter.',
  keywords: [
    'cybersecurity',
    'bug hunting',
    'osint',
    'vulnerability scanner',
    'penetration testing',
    'security tools',
    'indonesia',
    'hacking',
    'ethical hacking'
  ],
  authors: [{ name: 'KodeXGuard Team' }],
  creator: 'KodeXGuard',
  publisher: 'KodeXGuard',
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    type: 'website',
    locale: 'id_ID',
    url: 'https://kodexguard.com',
    title: 'KodeXGuard - Cybersecurity & Bug Hunting Platform',
    description: 'Platform mandiri cybersecurity & bug hunting Indonesia',
    siteName: 'KodeXGuard',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'KodeXGuard Platform',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'KodeXGuard - Cybersecurity & Bug Hunting Platform',
    description: 'Platform mandiri cybersecurity & bug hunting Indonesia',
    images: ['/og-image.png'],
    creator: '@kodexguard',
  },
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon-16x16.png',
    apple: '/apple-touch-icon.png',
  },
  manifest: '/site.webmanifest',
  viewport: {
    width: 'device-width',
    initialScale: 1,
    maximumScale: 1,
  },
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#0ea5e9' },
    { media: '(prefers-color-scheme: dark)', color: '#00f5ff' },
  ],
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="id" suppressHydrationWarning>
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      </head>
      <body 
        className={`${inter.variable} ${jetbrainsMono.variable} ${orbitron.variable} font-sans antialiased`}
        suppressHydrationWarning
      >
        <Providers>
          <div className="min-h-screen bg-gradient-to-br from-dark-950 via-dark-900 to-dark-800">
            {/* Background effects */}
            <div className="fixed inset-0 cyber-grid opacity-20 pointer-events-none" />
            <div className="fixed inset-0 bg-gradient-to-br from-neon-blue/5 via-transparent to-neon-purple/5 pointer-events-none" />
            
            {/* Main content */}
            <main className="relative z-10">
              {children}
            </main>
            
            {/* Toast notifications */}
            <Toaster
              position="top-right"
              toastOptions={{
                duration: 4000,
                className: 'backdrop-blur-sm',
                style: {
                  background: 'rgba(15, 23, 42, 0.9)',
                  color: '#fff',
                  border: '1px solid rgba(0, 245, 255, 0.3)',
                  borderRadius: '8px',
                },
                success: {
                  iconTheme: {
                    primary: '#22c55e',
                    secondary: '#fff',
                  },
                },
                error: {
                  iconTheme: {
                    primary: '#ef4444',
                    secondary: '#fff',
                  },
                },
              }}
            />
          </div>
        </Providers>
      </body>
    </html>
  );
}
