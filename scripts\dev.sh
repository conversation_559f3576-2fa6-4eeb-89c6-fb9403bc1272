#!/bin/bash

# KodeXGuard Development Server Script
# This script starts all development servers concurrently

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if port is in use
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null ; then
        return 0
    else
        return 1
    fi
}

# Function to kill process on port
kill_port() {
    local port=$1
    local pid=$(lsof -ti:$port)
    if [ ! -z "$pid" ]; then
        kill -9 $pid
        print_info "Killed process on port $port"
    fi
}

# Function to check prerequisites
check_prerequisites() {
    print_info "Checking prerequisites..."
    
    # Check if bun is installed
    if ! command -v bun &> /dev/null; then
        print_error "Bun.js is not installed. Please run setup.sh first."
        exit 1
    fi
    
    # Check if environment files exist
    if [ ! -f "api/.env" ]; then
        print_error "API environment file not found. Please run setup.sh first."
        exit 1
    fi
    
    if [ ! -f "web/.env.local" ]; then
        print_error "Web environment file not found. Please run setup.sh first."
        exit 1
    fi
    
    # Check if dependencies are installed
    if [ ! -d "node_modules" ]; then
        print_error "Dependencies not installed. Please run setup.sh first."
        exit 1
    fi
    
    print_success "Prerequisites check passed!"
}

# Function to start API server
start_api() {
    print_info "Starting API server..."
    
    # Check if port 3001 is in use
    if check_port 3001; then
        print_warning "Port 3001 is already in use. Killing existing process..."
        kill_port 3001
        sleep 2
    fi
    
    cd api
    bun run dev &
    API_PID=$!
    cd ..
    
    # Wait for API to start
    print_info "Waiting for API server to start..."
    for i in {1..30}; do
        if curl -s http://localhost:3001/health >/dev/null 2>&1; then
            print_success "API server started successfully on http://localhost:3001"
            break
        fi
        sleep 1
    done
    
    if [ $i -eq 30 ]; then
        print_error "API server failed to start within 30 seconds"
        kill $API_PID 2>/dev/null || true
        exit 1
    fi
}

# Function to start web server
start_web() {
    print_info "Starting web server..."
    
    # Check if port 3000 is in use
    if check_port 3000; then
        print_warning "Port 3000 is already in use. Killing existing process..."
        kill_port 3000
        sleep 2
    fi
    
    cd web
    bun run dev &
    WEB_PID=$!
    cd ..
    
    # Wait for web server to start
    print_info "Waiting for web server to start..."
    for i in {1..60}; do
        if curl -s http://localhost:3000 >/dev/null 2>&1; then
            print_success "Web server started successfully on http://localhost:3000"
            break
        fi
        sleep 1
    done
    
    if [ $i -eq 60 ]; then
        print_error "Web server failed to start within 60 seconds"
        kill $WEB_PID 2>/dev/null || true
        kill $API_PID 2>/dev/null || true
        exit 1
    fi
}

# Function to start mobile server (optional)
start_mobile() {
    if [ "$1" = "--mobile" ] || [ "$1" = "-m" ]; then
        print_info "Starting mobile development server..."
        
        # Check if Expo CLI is installed
        if ! command -v expo &> /dev/null; then
            print_warning "Expo CLI not found. Installing..."
            npm install -g @expo/cli
        fi
        
        cd mobile
        bun run start &
        MOBILE_PID=$!
        cd ..
        
        print_success "Mobile development server started!"
        print_info "Scan QR code with Expo Go app to test on device"
    fi
}

# Function to display server information
show_info() {
    echo
    echo -e "${CYAN}🛡️  KodeXGuard Development Servers${NC}"
    echo -e "${CYAN}====================================${NC}"
    echo
    echo -e "${GREEN}✅ API Server:${NC}      http://localhost:3001"
    echo -e "${GREEN}✅ Web App:${NC}        http://localhost:3000"
    echo -e "${GREEN}✅ API Docs:${NC}       http://localhost:3001/docs"
    echo -e "${GREEN}✅ Health Check:${NC}   http://localhost:3001/health"
    echo
    
    if [ ! -z "$MOBILE_PID" ]; then
        echo -e "${GREEN}✅ Mobile App:${NC}     Expo development server running"
        echo
    fi
    
    echo -e "${YELLOW}📝 Default Login:${NC}"
    echo -e "   Username: demo"
    echo -e "   Password: demo123"
    echo
    echo -e "${BLUE}🔧 Development Commands:${NC}"
    echo -e "   API logs:     cd api && bun run logs"
    echo -e "   Web logs:     cd web && bun run logs"
    echo -e "   Database:     cd api && bun run db:studio"
    echo -e "   Tests:        bun test"
    echo
    echo -e "${RED}⚠️  Press Ctrl+C to stop all servers${NC}"
    echo
}

# Function to cleanup on exit
cleanup() {
    echo
    print_info "Stopping development servers..."
    
    # Kill all background processes
    if [ ! -z "$API_PID" ]; then
        kill $API_PID 2>/dev/null || true
    fi
    
    if [ ! -z "$WEB_PID" ]; then
        kill $WEB_PID 2>/dev/null || true
    fi
    
    if [ ! -z "$MOBILE_PID" ]; then
        kill $MOBILE_PID 2>/dev/null || true
    fi
    
    # Kill any remaining processes on our ports
    kill_port 3000
    kill_port 3001
    
    print_success "All servers stopped. Goodbye! 👋"
    exit 0
}

# Function to show help
show_help() {
    echo "KodeXGuard Development Server Script"
    echo
    echo "Usage: $0 [OPTIONS]"
    echo
    echo "Options:"
    echo "  -h, --help     Show this help message"
    echo "  -m, --mobile   Also start mobile development server"
    echo "  --api-only     Start only API server"
    echo "  --web-only     Start only web server"
    echo "  --check        Check prerequisites only"
    echo
    echo "Examples:"
    echo "  $0              Start API and web servers"
    echo "  $0 --mobile    Start API, web, and mobile servers"
    echo "  $0 --api-only  Start only API server"
    echo "  $0 --check     Check prerequisites"
}

# Main function
main() {
    # Parse command line arguments
    case "$1" in
        -h|--help)
            show_help
            exit 0
            ;;
        --check)
            check_prerequisites
            exit 0
            ;;
        --api-only)
            check_prerequisites
            start_api
            show_info
            trap cleanup INT
            wait $API_PID
            ;;
        --web-only)
            check_prerequisites
            start_web
            show_info
            trap cleanup INT
            wait $WEB_PID
            ;;
        *)
            check_prerequisites
            start_api
            start_web
            start_mobile "$1"
            show_info
            
            # Set up signal handlers
            trap cleanup INT TERM
            
            # Wait for all background processes
            wait
            ;;
    esac
}

# Run main function with all arguments
main "$@"
