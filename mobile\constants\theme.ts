export const colors = {
  // Primary colors
  primary: '#00f5ff', // neon-blue
  secondary: '#bf00ff', // neon-purple
  tertiary: '#39ff14', // neon-green
  
  // Neon colors
  neon: {
    blue: '#00f5ff',
    purple: '#bf00ff',
    green: '#39ff14',
    pink: '#ff1493',
    orange: '#ff6600',
  },
  
  // Dark theme colors
  dark: {
    50: '#f8fafc',
    100: '#f1f5f9',
    200: '#e2e8f0',
    300: '#cbd5e1',
    400: '#94a3b8',
    500: '#64748b',
    600: '#475569',
    700: '#334155',
    800: '#1e293b',
    900: '#0f172a',
    950: '#020617',
  },
  
  // Gray scale
  gray: {
    50: '#f9fafb',
    100: '#f3f4f6',
    200: '#e5e7eb',
    300: '#d1d5db',
    400: '#9ca3af',
    500: '#6b7280',
    600: '#4b5563',
    700: '#374151',
    800: '#1f2937',
    900: '#111827',
  },
  
  // Status colors
  success: '#22c55e',
  warning: '#f59e0b',
  error: '#ef4444',
  info: '#3b82f6',
  
  // Common colors
  white: '#ffffff',
  black: '#000000',
  transparent: 'transparent',
  
  // Severity colors
  severity: {
    critical: '#dc2626',
    high: '#ea580c',
    medium: '#d97706',
    low: '#16a34a',
  },
  
  // Plan colors
  plan: {
    free: '#6b7280',
    student: '#3b82f6',
    hobby: '#8b5cf6',
    bughunter: '#f59e0b',
    cybersecurity: '#dc2626',
  },
};

export const typography = {
  fonts: {
    regular: 'Inter-Regular',
    medium: 'Inter-Medium',
    semiBold: 'Inter-SemiBold',
    bold: 'Inter-Bold',
    mono: 'JetBrainsMono-Regular',
    monoBold: 'JetBrainsMono-Bold',
  },
  sizes: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 30,
    '4xl': 36,
    '5xl': 48,
    '6xl': 60,
  },
  lineHeights: {
    tight: 1.25,
    snug: 1.375,
    normal: 1.5,
    relaxed: 1.625,
    loose: 2,
  },
};

export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  '2xl': 48,
  '3xl': 64,
  '4xl': 80,
  '5xl': 96,
};

export const borderRadius = {
  none: 0,
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  '2xl': 24,
  full: 9999,
};

export const shadows = {
  sm: {
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  md: {
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 3,
  },
  lg: {
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.15,
    shadowRadius: 15,
    elevation: 5,
  },
  neon: {
    shadowColor: colors.neon.blue,
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.8,
    shadowRadius: 10,
    elevation: 8,
  },
};

export const layout = {
  window: {
    width: '100%',
    height: '100%',
  },
  container: {
    paddingHorizontal: spacing.md,
  },
  section: {
    marginBottom: spacing.lg,
  },
};

export const animations = {
  timing: {
    fast: 200,
    normal: 300,
    slow: 500,
  },
  easing: {
    linear: 'linear',
    ease: 'ease',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out',
  },
};

// Component-specific themes
export const components = {
  button: {
    primary: {
      backgroundColor: colors.neon.blue,
      color: colors.dark[900],
      borderColor: colors.neon.blue,
    },
    secondary: {
      backgroundColor: colors.transparent,
      color: colors.neon.blue,
      borderColor: colors.neon.blue,
    },
    danger: {
      backgroundColor: colors.error,
      color: colors.white,
      borderColor: colors.error,
    },
  },
  card: {
    default: {
      backgroundColor: colors.dark[800],
      borderColor: colors.dark[600],
      borderRadius: borderRadius.lg,
    },
    glow: {
      backgroundColor: colors.dark[800],
      borderColor: colors.neon.blue,
      borderRadius: borderRadius.lg,
      ...shadows.neon,
    },
  },
  input: {
    default: {
      backgroundColor: colors.dark[800],
      borderColor: colors.dark[600],
      color: colors.white,
      placeholderColor: colors.gray[400],
    },
    focused: {
      borderColor: colors.neon.blue,
    },
    error: {
      borderColor: colors.error,
    },
  },
};

export default {
  colors,
  typography,
  spacing,
  borderRadius,
  shadows,
  layout,
  animations,
  components,
};
