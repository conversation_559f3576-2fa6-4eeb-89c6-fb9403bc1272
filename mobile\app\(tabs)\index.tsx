import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  RefreshControl,
} from 'react-native';
import { router } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import {
  MagnifyingGlassIcon,
  ShieldCheckIcon,
  DocumentMagnifyingGlassIcon,
  BugAntIcon,
  ChartBarIcon,
  TrophyIcon,
  FireIcon,
  WifiIcon,
  SignalSlashIcon,
} from 'react-native-heroicons/outline';
import { useAuth } from '../../lib/auth-context';
import { useNetwork } from '../../lib/network-context';
import { useDatabase } from '../../lib/database-context';
import { colors, typography, spacing, components } from '../../constants/theme';
import { userAPI, cveAPI, leaderboardAPI } from '../../lib/api';
import { useQuery } from 'react-query';

const { width } = Dimensions.get('window');

export default function DashboardScreen() {
  const { user } = useAuth();
  const { isConnected } = useNetwork();
  const { getOSINTResults, getVulnScans, getFileAnalyses } = useDatabase();
  const [refreshing, setRefreshing] = useState(false);
  const [localStats, setLocalStats] = useState({
    osintQueries: 0,
    vulnScans: 0,
    fileAnalyses: 0,
  });

  // Fetch online stats
  const { data: onlineStats, refetch: refetchStats } = useQuery(
    'user-stats',
    () => userAPI.getStats().then(res => res.data.data),
    { enabled: isConnected }
  );

  // Fetch recent CVEs
  const { data: recentCVEs } = useQuery(
    'recent-cves',
    () => cveAPI.getRecent(3).then(res => res.data.data),
    { enabled: isConnected }
  );

  // Fetch leaderboard position
  const { data: leaderboardPosition } = useQuery(
    'leaderboard-position',
    () => leaderboardAPI.getMyPosition().then(res => res.data.data),
    { enabled: isConnected }
  );

  useEffect(() => {
    loadLocalStats();
  }, []);

  const loadLocalStats = async () => {
    try {
      const [osintResults, vulnScans, fileAnalyses] = await Promise.all([
        getOSINTResults(),
        getVulnScans(),
        getFileAnalyses(),
      ]);

      setLocalStats({
        osintQueries: osintResults.length,
        vulnScans: vulnScans.length,
        fileAnalyses: fileAnalyses.length,
      });
    } catch (error) {
      console.error('Error loading local stats:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadLocalStats();
    if (isConnected) {
      await refetchStats();
    }
    setRefreshing(false);
  };

  const quickActions = [
    {
      title: 'OSINT Search',
      subtitle: 'Investigasi OSINT',
      icon: MagnifyingGlassIcon,
      color: colors.neon.blue,
      onPress: () => router.push('/scan?type=osint'),
    },
    {
      title: 'Vuln Scan',
      subtitle: 'Scan kerentanan',
      icon: ShieldCheckIcon,
      color: colors.neon.purple,
      onPress: () => router.push('/scan?type=vulnerability'),
    },
    {
      title: 'File Analysis',
      subtitle: 'Analisis file',
      icon: DocumentMagnifyingGlassIcon,
      color: colors.neon.green,
      onPress: () => router.push('/scan?type=file'),
    },
    {
      title: 'CVE Database',
      subtitle: 'Database CVE',
      icon: BugAntIcon,
      color: colors.severity.critical,
      onPress: () => router.push('/security?tab=cve'),
    },
  ];

  const stats = isConnected ? onlineStats : localStats;

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={onRefresh}
          tintColor={colors.neon.blue}
          colors={[colors.neon.blue]}
        />
      }
    >
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <Text style={styles.greeting}>Selamat datang kembali,</Text>
          <Text style={styles.userName}>{user?.fullName}! 👋</Text>
          <View style={styles.connectionStatus}>
            {isConnected ? (
              <View style={styles.onlineStatus}>
                <WifiIcon size={16} color={colors.neon.green} />
                <Text style={styles.statusText}>Online</Text>
              </View>
            ) : (
              <View style={styles.offlineStatus}>
                <SignalSlashIcon size={16} color={colors.warning} />
                <Text style={styles.statusText}>Offline</Text>
              </View>
            )}
          </View>
        </View>
      </View>

      {/* Stats Cards */}
      <View style={styles.statsContainer}>
        <View style={styles.statsGrid}>
          <View style={styles.statCard}>
            <Text style={styles.statValue}>{stats?.totalScans || localStats.osintQueries}</Text>
            <Text style={styles.statLabel}>OSINT Queries</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statValue}>{stats?.totalVulnerabilities || localStats.vulnScans}</Text>
            <Text style={styles.statLabel}>Vuln Scans</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statValue}>{stats?.totalFiles || localStats.fileAnalyses}</Text>
            <Text style={styles.statLabel}>File Analysis</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statValue}>{stats?.apiCalls || 0}</Text>
            <Text style={styles.statLabel}>API Calls</Text>
          </View>
        </View>
      </View>

      {/* Quick Actions */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Quick Actions</Text>
        <View style={styles.actionsGrid}>
          {quickActions.map((action, index) => (
            <TouchableOpacity
              key={index}
              style={styles.actionCard}
              onPress={action.onPress}
              activeOpacity={0.8}
            >
              <LinearGradient
                colors={[colors.dark[800], colors.dark[700]]}
                style={styles.actionGradient}
              >
                <View style={[styles.actionIcon, { backgroundColor: `${action.color}20` }]}>
                  <action.icon size={24} color={action.color} />
                </View>
                <Text style={styles.actionTitle}>{action.title}</Text>
                <Text style={styles.actionSubtitle}>{action.subtitle}</Text>
              </LinearGradient>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Plan Status */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Plan Status</Text>
        <View style={styles.planCard}>
          <View style={styles.planHeader}>
            <Text style={styles.planName}>{user?.plan?.toUpperCase()}</Text>
            <View style={[styles.planBadge, { backgroundColor: colors.plan[user?.plan || 'free'] }]}>
              <Text style={styles.planBadgeText}>Active</Text>
            </View>
          </View>
          <View style={styles.planUsage}>
            <View style={styles.usageItem}>
              <Text style={styles.usageLabel}>OSINT Queries</Text>
              <Text style={styles.usageValue}>{localStats.osintQueries}/50</Text>
            </View>
            <View style={styles.usageItem}>
              <Text style={styles.usageLabel}>Vuln Scans</Text>
              <Text style={styles.usageValue}>{localStats.vulnScans}/25</Text>
            </View>
            <View style={styles.usageItem}>
              <Text style={styles.usageLabel}>File Analysis</Text>
              <Text style={styles.usageValue}>{localStats.fileAnalyses}/15</Text>
            </View>
          </View>
        </View>
      </View>

      {/* Leaderboard */}
      {isConnected && leaderboardPosition && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Leaderboard</Text>
          <View style={styles.leaderboardCard}>
            <View style={styles.leaderboardHeader}>
              <TrophyIcon size={24} color={colors.warning} />
              <Text style={styles.leaderboardRank}>#{leaderboardPosition.rank || 'N/A'}</Text>
            </View>
            <Text style={styles.leaderboardLabel}>Peringkat Anda</Text>
            <View style={styles.leaderboardStats}>
              <View style={styles.leaderboardStat}>
                <Text style={styles.leaderboardStatValue}>{leaderboardPosition.score || 0}</Text>
                <Text style={styles.leaderboardStatLabel}>Score</Text>
              </View>
              <View style={styles.leaderboardStat}>
                <Text style={styles.leaderboardStatValue}>{leaderboardPosition.totalScans || 0}</Text>
                <Text style={styles.leaderboardStatLabel}>Scans</Text>
              </View>
              <View style={styles.leaderboardStat}>
                <Text style={styles.leaderboardStatValue}>{leaderboardPosition.totalVulns || 0}</Text>
                <Text style={styles.leaderboardStatLabel}>Vulns</Text>
              </View>
            </View>
          </View>
        </View>
      )}

      {/* Recent CVEs */}
      {isConnected && recentCVEs && (
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>CVE Terbaru</Text>
            <FireIcon size={20} color={colors.severity.critical} />
          </View>
          <View style={styles.cveList}>
            {recentCVEs.slice(0, 3).map((cve: any, index: number) => (
              <View key={cve.id} style={styles.cveItem}>
                <View style={styles.cveHeader}>
                  <Text style={styles.cveId}>{cve.cveId}</Text>
                  <View style={[styles.severityBadge, { backgroundColor: colors.severity[cve.severity] }]}>
                    <Text style={styles.severityText}>{cve.severity?.toUpperCase()}</Text>
                  </View>
                </View>
                <Text style={styles.cveDescription} numberOfLines={2}>
                  {cve.description}
                </Text>
                <Text style={styles.cvssScore}>CVSS: {cve.cvssScore || 'N/A'}</Text>
              </View>
            ))}
          </View>
        </View>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.dark[950],
  },
  header: {
    padding: spacing.md,
    paddingTop: spacing.lg,
  },
  headerContent: {
    marginBottom: spacing.md,
  },
  greeting: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fonts.regular,
    color: colors.gray[400],
  },
  userName: {
    fontSize: typography.sizes['2xl'],
    fontFamily: typography.fonts.bold,
    color: colors.white,
    marginBottom: spacing.sm,
  },
  connectionStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  onlineStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: `${colors.neon.green}20`,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
  },
  offlineStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: `${colors.warning}20`,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
  },
  statusText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fonts.medium,
    color: colors.white,
    marginLeft: spacing.xs,
  },
  statsContainer: {
    paddingHorizontal: spacing.md,
    marginBottom: spacing.lg,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statCard: {
    width: (width - spacing.md * 2 - spacing.sm) / 2,
    backgroundColor: colors.dark[800],
    borderRadius: 12,
    padding: spacing.md,
    marginBottom: spacing.sm,
    borderWidth: 1,
    borderColor: colors.dark[600],
  },
  statValue: {
    fontSize: typography.sizes['2xl'],
    fontFamily: typography.fonts.bold,
    color: colors.white,
    marginBottom: spacing.xs,
  },
  statLabel: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fonts.regular,
    color: colors.gray[400],
  },
  section: {
    paddingHorizontal: spacing.md,
    marginBottom: spacing.lg,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: spacing.md,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fonts.semiBold,
    color: colors.white,
    marginBottom: spacing.md,
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionCard: {
    width: (width - spacing.md * 2 - spacing.sm) / 2,
    marginBottom: spacing.sm,
  },
  actionGradient: {
    borderRadius: 12,
    padding: spacing.md,
    borderWidth: 1,
    borderColor: colors.dark[600],
  },
  actionIcon: {
    width: 48,
    height: 48,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  actionTitle: {
    fontSize: typography.sizes.base,
    fontFamily: typography.fonts.semiBold,
    color: colors.white,
    marginBottom: spacing.xs,
  },
  actionSubtitle: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fonts.regular,
    color: colors.gray[400],
  },
  planCard: {
    backgroundColor: colors.dark[800],
    borderRadius: 12,
    padding: spacing.md,
    borderWidth: 1,
    borderColor: colors.dark[600],
  },
  planHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  planName: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fonts.bold,
    color: colors.white,
  },
  planBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 8,
  },
  planBadgeText: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fonts.medium,
    color: colors.white,
  },
  planUsage: {
    gap: spacing.sm,
  },
  usageItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  usageLabel: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fonts.regular,
    color: colors.gray[400],
  },
  usageValue: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fonts.medium,
    color: colors.white,
  },
  leaderboardCard: {
    backgroundColor: colors.dark[800],
    borderRadius: 12,
    padding: spacing.md,
    borderWidth: 1,
    borderColor: colors.dark[600],
    alignItems: 'center',
  },
  leaderboardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  leaderboardRank: {
    fontSize: typography.sizes['3xl'],
    fontFamily: typography.fonts.bold,
    color: colors.white,
    marginLeft: spacing.sm,
  },
  leaderboardLabel: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fonts.regular,
    color: colors.gray[400],
    marginBottom: spacing.md,
  },
  leaderboardStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
  },
  leaderboardStat: {
    alignItems: 'center',
  },
  leaderboardStatValue: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fonts.bold,
    color: colors.white,
  },
  leaderboardStatLabel: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fonts.regular,
    color: colors.gray[400],
  },
  cveList: {
    gap: spacing.sm,
  },
  cveItem: {
    backgroundColor: colors.dark[800],
    borderRadius: 12,
    padding: spacing.md,
    borderWidth: 1,
    borderColor: colors.dark[600],
  },
  cveHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  cveId: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fonts.mono,
    color: colors.neon.blue,
  },
  severityBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 6,
  },
  severityText: {
    fontSize: typography.sizes.xs,
    fontFamily: typography.fonts.medium,
    color: colors.white,
  },
  cveDescription: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fonts.regular,
    color: colors.gray[400],
    marginBottom: spacing.sm,
  },
  cvssScore: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fonts.medium,
    color: colors.white,
  },
});
