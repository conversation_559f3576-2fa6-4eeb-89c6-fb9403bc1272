import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import { db } from '../database/connection';
import { plans, payments, users } from '../database/schema';
import { eq } from 'drizzle-orm';
import { generateId, UserPlan, PlanDuration } from '@kodexguard/shared';
import { asyncHandler, validationError, notFoundError } from '../middleware/error-handler';

const planRoutes = new Hono();

// Get all available plans
planRoutes.get('/', asyncHandler(async (c) => {
  const availablePlans = await db
    .select()
    .from(plans)
    .where(eq(plans.isActive, true));

  return c.json({
    success: true,
    data: availablePlans
  });
}));

// Get plan details
planRoutes.get('/:id', asyncHandler(async (c) => {
  const planId = c.req.param('id');

  const [plan] = await db
    .select()
    .from(plans)
    .where(eq(plans.id, planId))
    .limit(1);

  if (!plan) {
    throw notFoundError('Plan');
  }

  return c.json({
    success: true,
    data: plan
  });
}));

// Purchase plan
const purchasePlanSchema = z.object({
  planId: z.string(),
  gateway: z.enum(['tripay', 'midtrans', 'xendit', 'manual']),
  duration: z.enum(['daily', 'weekly', 'monthly', 'yearly']).optional()
});

planRoutes.post('/purchase', zValidator('json', purchasePlanSchema), asyncHandler(async (c) => {
  const user = c.get('user');
  const { planId, gateway, duration = 'monthly' } = c.req.valid('json');

  // Get plan details
  const [plan] = await db
    .select()
    .from(plans)
    .where(eq(plans.id, planId))
    .limit(1);

  if (!plan || !plan.isActive) {
    throw notFoundError('Plan');
  }

  // Calculate amount based on duration
  const basePrice = plan.price;
  let amount = basePrice;
  
  switch (duration) {
    case 'daily':
      amount = basePrice / 30;
      break;
    case 'weekly':
      amount = basePrice / 4;
      break;
    case 'monthly':
      amount = basePrice;
      break;
    case 'yearly':
      amount = basePrice * 10; // 2 months free
      break;
  }

  // Create payment record
  const paymentId = generateId();
  await db.insert(payments).values({
    id: paymentId,
    userId: user.id,
    planId,
    amount,
    currency: 'IDR',
    gateway,
    status: 'pending',
    metadata: {
      duration,
      planName: plan.name,
      planType: plan.type
    }
  });

  // Generate payment URL based on gateway
  let paymentUrl = '';
  let instructions = '';

  switch (gateway) {
    case 'manual':
      instructions = `Transfer Rp ${amount.toLocaleString('id-ID')} ke rekening berikut:
      
BCA: 1234567890
BNI: 0987654321
Mandiri: 1122334455

Kirim bukti transfer ke WhatsApp: +62-xxx-xxxx-xxxx
Sertakan kode pembayaran: ${paymentId}`;
      break;
    case 'tripay':
      paymentUrl = `https://tripay.co.id/checkout/${paymentId}`;
      break;
    case 'midtrans':
      paymentUrl = `https://app.midtrans.com/snap/v1/transactions/${paymentId}`;
      break;
    case 'xendit':
      paymentUrl = `https://checkout.xendit.co/web/${paymentId}`;
      break;
  }

  return c.json({
    success: true,
    message: 'Payment created successfully',
    data: {
      paymentId,
      amount,
      currency: 'IDR',
      gateway,
      paymentUrl,
      instructions,
      plan: {
        id: plan.id,
        name: plan.name,
        type: plan.type,
        duration
      }
    }
  }, 201);
}));

// Get payment status
planRoutes.get('/payment/:id', asyncHandler(async (c) => {
  const user = c.get('user');
  const paymentId = c.req.param('id');

  const [payment] = await db
    .select()
    .from(payments)
    .where(eq(payments.id, paymentId))
    .limit(1);

  if (!payment || payment.userId !== user.id) {
    throw notFoundError('Payment');
  }

  return c.json({
    success: true,
    data: payment
  });
}));

// Get user's payment history
planRoutes.get('/payments/history', asyncHandler(async (c) => {
  const user = c.get('user');
  const page = parseInt(c.req.query('page') || '1');
  const limit = parseInt(c.req.query('limit') || '20');
  const offset = (page - 1) * limit;

  const userPayments = await db
    .select({
      id: payments.id,
      planId: payments.planId,
      amount: payments.amount,
      currency: payments.currency,
      gateway: payments.gateway,
      status: payments.status,
      metadata: payments.metadata,
      createdAt: payments.createdAt,
      updatedAt: payments.updatedAt
    })
    .from(payments)
    .where(eq(payments.userId, user.id))
    .orderBy(payments.createdAt)
    .limit(limit)
    .offset(offset);

  return c.json({
    success: true,
    data: userPayments,
    pagination: {
      page,
      limit,
      total: userPayments.length
    }
  });
}));

// Get current user plan info
planRoutes.get('/current', asyncHandler(async (c) => {
  const user = c.get('user');

  const [userInfo] = await db
    .select({
      plan: users.plan,
      planExpiry: users.planExpiry
    })
    .from(users)
    .where(eq(users.id, user.id))
    .limit(1);

  if (!userInfo) {
    throw notFoundError('User');
  }

  // Get plan details
  const [planDetails] = await db
    .select()
    .from(plans)
    .where(eq(plans.type, userInfo.plan as UserPlan))
    .limit(1);

  const isExpired = userInfo.planExpiry ? new Date() > userInfo.planExpiry : false;

  return c.json({
    success: true,
    data: {
      currentPlan: userInfo.plan,
      planExpiry: userInfo.planExpiry,
      isExpired,
      planDetails: planDetails || null,
      daysRemaining: userInfo.planExpiry 
        ? Math.max(0, Math.ceil((userInfo.planExpiry.getTime() - Date.now()) / (1000 * 60 * 60 * 24)))
        : null
    }
  });
}));

// Plan comparison
planRoutes.get('/compare', asyncHandler(async (c) => {
  const allPlans = await db
    .select()
    .from(plans)
    .where(eq(plans.isActive, true));

  const comparison = allPlans.map(plan => ({
    id: plan.id,
    name: plan.name,
    type: plan.type,
    price: plan.price,
    features: plan.features,
    limits: plan.limits,
    recommended: plan.type === 'hobby' // Mark hobby as recommended
  }));

  return c.json({
    success: true,
    data: comparison
  });
}));

// Upgrade/downgrade plan
const changePlanSchema = z.object({
  newPlanType: z.enum(['free', 'student', 'hobby', 'bughunter', 'cybersecurity'])
});

planRoutes.post('/change', zValidator('json', changePlanSchema), asyncHandler(async (c) => {
  const user = c.get('user');
  const { newPlanType } = c.req.valid('json');

  // Check if downgrading to free
  if (newPlanType === 'free') {
    await db
      .update(users)
      .set({
        plan: newPlanType,
        planExpiry: null,
        updatedAt: new Date()
      })
      .where(eq(users.id, user.id));

    return c.json({
      success: true,
      message: 'Plan changed to free successfully'
    });
  }

  // For paid plans, require payment
  return c.json({
    success: false,
    error: 'Payment required for plan upgrade',
    message: 'Please use the purchase endpoint to upgrade to a paid plan'
  }, 400);
}));

export default planRoutes;
