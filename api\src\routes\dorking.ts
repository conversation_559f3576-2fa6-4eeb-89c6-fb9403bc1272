import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import { db } from '../database/connection';
import { dorkPresets } from '../database/schema';
import { eq, like, and, or } from 'drizzle-orm';
import { generateId } from '@kodexguard/shared';
import { asyncHandler, validationError, notFoundError } from '../middleware/error-handler';

const dorkRoutes = new Hono();

// Get dork presets
const getDorksSchema = z.object({
  category: z.string().optional(),
  search: z.string().optional(),
  page: z.string().optional(),
  limit: z.string().optional()
});

dorkRoutes.get('/presets', zValidator('query', getDorksSchema), asyncHandler(async (c) => {
  const {
    category,
    search,
    page = '1',
    limit = '20'
  } = c.req.valid('query');

  const pageNum = parseInt(page);
  const limitNum = parseInt(limit);
  const offset = (pageNum - 1) * limitNum;

  // Build where conditions
  const conditions = [eq(dorkPresets.isPublic, true)];

  if (category) {
    conditions.push(eq(dorkPresets.category, category));
  }

  if (search) {
    conditions.push(
      or(
        like(dorkPresets.name, `%${search}%`),
        like(dorkPresets.query, `%${search}%`),
        like(dorkPresets.description, `%${search}%`)
      )
    );
  }

  const whereClause = and(...conditions);

  const dorks = await db
    .select()
    .from(dorkPresets)
    .where(whereClause)
    .limit(limitNum)
    .offset(offset);

  return c.json({
    success: true,
    data: dorks,
    pagination: {
      page: pageNum,
      limit: limitNum,
      total: dorks.length
    }
  });
}));

// Get dork categories
dorkRoutes.get('/categories', asyncHandler(async (c) => {
  const categories = await db
    .selectDistinct({ category: dorkPresets.category })
    .from(dorkPresets)
    .where(eq(dorkPresets.isPublic, true));

  const categoryList = categories.map(c => c.category);

  return c.json({
    success: true,
    data: categoryList
  });
}));

// Get popular dorks
dorkRoutes.get('/popular', asyncHandler(async (c) => {
  const limit = parseInt(c.req.query('limit') || '10');

  const popularDorks = await db
    .select()
    .from(dorkPresets)
    .where(eq(dorkPresets.isPublic, true))
    .orderBy(dorkPresets.usageCount)
    .limit(limit);

  return c.json({
    success: true,
    data: popularDorks
  });
}));

// Get dork by ID
dorkRoutes.get('/presets/:id', asyncHandler(async (c) => {
  const dorkId = c.req.param('id');

  const [dork] = await db
    .select()
    .from(dorkPresets)
    .where(eq(dorkPresets.id, dorkId))
    .limit(1);

  if (!dork) {
    throw notFoundError('Dork preset');
  }

  // Increment usage count
  await db
    .update(dorkPresets)
    .set({ usageCount: dork.usageCount + 1 })
    .where(eq(dorkPresets.id, dorkId));

  return c.json({
    success: true,
    data: dork
  });
}));

// Create custom dork
const createDorkSchema = z.object({
  name: z.string().min(1).max(100),
  query: z.string().min(1).max(1000),
  category: z.string().min(1).max(50),
  description: z.string().max(500).optional(),
  isPublic: z.boolean().optional()
});

dorkRoutes.post('/presets', zValidator('json', createDorkSchema), asyncHandler(async (c) => {
  const user = c.get('user');
  const { name, query, category, description, isPublic = false } = c.req.valid('json');

  const dorkId = generateId();
  await db.insert(dorkPresets).values({
    id: dorkId,
    name,
    query,
    category,
    description,
    isPublic,
    createdBy: user.id,
    usageCount: 0
  });

  return c.json({
    success: true,
    message: 'Dork preset created successfully',
    data: { id: dorkId, name, category }
  }, 201);
}));

// Get user's custom dorks
dorkRoutes.get('/my-dorks', asyncHandler(async (c) => {
  const user = c.get('user');
  const page = parseInt(c.req.query('page') || '1');
  const limit = parseInt(c.req.query('limit') || '20');
  const offset = (page - 1) * limit;

  const userDorks = await db
    .select()
    .from(dorkPresets)
    .where(eq(dorkPresets.createdBy, user.id))
    .limit(limit)
    .offset(offset);

  return c.json({
    success: true,
    data: userDorks,
    pagination: {
      page,
      limit,
      total: userDorks.length
    }
  });
}));

// Update custom dork
const updateDorkSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  query: z.string().min(1).max(1000).optional(),
  category: z.string().min(1).max(50).optional(),
  description: z.string().max(500).optional(),
  isPublic: z.boolean().optional()
});

dorkRoutes.put('/presets/:id', zValidator('json', updateDorkSchema), asyncHandler(async (c) => {
  const user = c.get('user');
  const dorkId = c.req.param('id');
  const updateData = c.req.valid('json');

  // Check if dork belongs to user
  const [existingDork] = await db
    .select({ createdBy: dorkPresets.createdBy })
    .from(dorkPresets)
    .where(eq(dorkPresets.id, dorkId))
    .limit(1);

  if (!existingDork) {
    throw notFoundError('Dork preset');
  }

  if (existingDork.createdBy !== user.id && user.role !== 'super_admin') {
    throw validationError('You can only edit your own dork presets');
  }

  await db
    .update(dorkPresets)
    .set({
      ...updateData,
      updatedAt: new Date()
    })
    .where(eq(dorkPresets.id, dorkId));

  return c.json({
    success: true,
    message: 'Dork preset updated successfully'
  });
}));

// Delete custom dork
dorkRoutes.delete('/presets/:id', asyncHandler(async (c) => {
  const user = c.get('user');
  const dorkId = c.req.param('id');

  // Check if dork belongs to user
  const [existingDork] = await db
    .select({ createdBy: dorkPresets.createdBy })
    .from(dorkPresets)
    .where(eq(dorkPresets.id, dorkId))
    .limit(1);

  if (!existingDork) {
    throw notFoundError('Dork preset');
  }

  if (existingDork.createdBy !== user.id && user.role !== 'super_admin') {
    throw validationError('You can only delete your own dork presets');
  }

  await db
    .delete(dorkPresets)
    .where(eq(dorkPresets.id, dorkId));

  return c.json({
    success: true,
    message: 'Dork preset deleted successfully'
  });
}));

// Execute Google dork search
const executeDorkSchema = z.object({
  query: z.string().min(1).max(1000),
  limit: z.number().min(1).max(100).optional()
});

dorkRoutes.post('/search', zValidator('json', executeDorkSchema), asyncHandler(async (c) => {
  const { query, limit = 10 } = c.req.valid('json');

  // Mock Google dork search results (replace with actual implementation)
  const mockResults = Array.from({ length: Math.min(limit, 5) }, (_, i) => ({
    title: `Search Result ${i + 1} for: ${query}`,
    url: `https://example${i + 1}.com/result`,
    snippet: `This is a mock search result snippet for the dork query: ${query}`,
    domain: `example${i + 1}.com`,
    cached: `https://webcache.googleusercontent.com/search?q=cache:example${i + 1}.com`
  }));

  return c.json({
    success: true,
    data: {
      query,
      results: mockResults,
      count: mockResults.length,
      timestamp: new Date().toISOString()
    }
  });
}));

// Get dork statistics
dorkRoutes.get('/stats', asyncHandler(async (c) => {
  const allDorks = await db
    .select({
      category: dorkPresets.category,
      isPublic: dorkPresets.isPublic,
      usageCount: dorkPresets.usageCount
    })
    .from(dorkPresets);

  const stats = {
    total: allDorks.length,
    public: allDorks.filter(d => d.isPublic).length,
    private: allDorks.filter(d => !d.isPublic).length,
    totalUsage: allDorks.reduce((sum, d) => sum + d.usageCount, 0),
    byCategory: allDorks.reduce((acc, d) => {
      acc[d.category] = (acc[d.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
    mostUsed: allDorks
      .sort((a, b) => b.usageCount - a.usageCount)
      .slice(0, 5)
      .map(d => ({ category: d.category, usage: d.usageCount }))
  };

  return c.json({
    success: true,
    data: stats
  });
}));

// Get daily dork suggestions
dorkRoutes.get('/daily', asyncHandler(async (c) => {
  const today = new Date();
  const dayOfYear = Math.floor((today.getTime() - new Date(today.getFullYear(), 0, 0).getTime()) / (1000 * 60 * 60 * 24));
  
  // Get 5 random dorks based on day of year (consistent daily selection)
  const allDorks = await db
    .select()
    .from(dorkPresets)
    .where(eq(dorkPresets.isPublic, true));

  // Simple pseudo-random selection based on day
  const selectedDorks = [];
  for (let i = 0; i < Math.min(5, allDorks.length); i++) {
    const index = (dayOfYear + i * 7) % allDorks.length;
    selectedDorks.push(allDorks[index]);
  }

  return c.json({
    success: true,
    data: {
      date: today.toISOString().split('T')[0],
      dorks: selectedDorks
    }
  });
}));

export default dorkRoutes;
