import { Context } from 'hono';
import { HTTPException } from 'hono/http-exception';

export interface AppError extends Error {
  statusCode?: number;
  code?: string;
  details?: any;
}

export function createError(message: string, statusCode: number = 500, code?: string, details?: any): AppError {
  const error = new Error(message) as AppError;
  error.statusCode = statusCode;
  error.code = code;
  error.details = details;
  return error;
}

export async function errorHandler(err: Error | HTTPException, c: Context) {
  console.error('Error occurred:', {
    message: err.message,
    stack: err.stack,
    url: c.req.url,
    method: c.req.method,
    timestamp: new Date().toISOString()
  });

  // Handle HTTP exceptions from Hono
  if (err instanceof HTTPException) {
    return c.json({
      success: false,
      error: err.message,
      code: 'HTTP_EXCEPTION',
      timestamp: new Date().toISOString()
    }, err.status);
  }

  // Handle custom app errors
  const appError = err as AppError;
  const statusCode = appError.statusCode || 500;
  const code = appError.code || 'INTERNAL_ERROR';

  // Handle specific error types
  switch (code) {
    case 'VALIDATION_ERROR':
      return c.json({
        success: false,
        error: 'Validation failed',
        message: appError.message,
        details: appError.details,
        code,
        timestamp: new Date().toISOString()
      }, 400);

    case 'UNAUTHORIZED':
      return c.json({
        success: false,
        error: 'Unauthorized access',
        message: appError.message,
        code,
        timestamp: new Date().toISOString()
      }, 401);

    case 'FORBIDDEN':
      return c.json({
        success: false,
        error: 'Access forbidden',
        message: appError.message,
        code,
        timestamp: new Date().toISOString()
      }, 403);

    case 'NOT_FOUND':
      return c.json({
        success: false,
        error: 'Resource not found',
        message: appError.message,
        code,
        timestamp: new Date().toISOString()
      }, 404);

    case 'RATE_LIMIT_EXCEEDED':
      return c.json({
        success: false,
        error: 'Rate limit exceeded',
        message: appError.message,
        code,
        timestamp: new Date().toISOString()
      }, 429);

    case 'PLAN_LIMIT_EXCEEDED':
      return c.json({
        success: false,
        error: 'Plan limit exceeded',
        message: appError.message,
        details: appError.details,
        code,
        timestamp: new Date().toISOString()
      }, 429);

    case 'DATABASE_ERROR':
      return c.json({
        success: false,
        error: 'Database operation failed',
        message: process.env.NODE_ENV === 'development' ? appError.message : 'Internal server error',
        code,
        timestamp: new Date().toISOString()
      }, 500);

    case 'EXTERNAL_API_ERROR':
      return c.json({
        success: false,
        error: 'External service unavailable',
        message: appError.message,
        code,
        timestamp: new Date().toISOString()
      }, 503);

    default:
      // Generic server error
      return c.json({
        success: false,
        error: 'Internal server error',
        message: process.env.NODE_ENV === 'development' ? appError.message : 'Something went wrong',
        code: 'INTERNAL_ERROR',
        timestamp: new Date().toISOString()
      }, statusCode);
  }
}

// Async error wrapper for route handlers
export function asyncHandler(fn: Function) {
  return (c: Context, next?: any) => {
    return Promise.resolve(fn(c, next)).catch((error) => {
      throw error;
    });
  };
}

// Validation error helper
export function validationError(message: string, details?: any): AppError {
  return createError(message, 400, 'VALIDATION_ERROR', details);
}

// Not found error helper
export function notFoundError(resource: string = 'Resource'): AppError {
  return createError(`${resource} not found`, 404, 'NOT_FOUND');
}

// Unauthorized error helper
export function unauthorizedError(message: string = 'Unauthorized access'): AppError {
  return createError(message, 401, 'UNAUTHORIZED');
}

// Forbidden error helper
export function forbiddenError(message: string = 'Access forbidden'): AppError {
  return createError(message, 403, 'FORBIDDEN');
}

// Rate limit error helper
export function rateLimitError(message: string = 'Rate limit exceeded'): AppError {
  return createError(message, 429, 'RATE_LIMIT_EXCEEDED');
}

// Plan limit error helper
export function planLimitError(message: string, details?: any): AppError {
  return createError(message, 429, 'PLAN_LIMIT_EXCEEDED', details);
}

// Database error helper
export function databaseError(message: string): AppError {
  return createError(message, 500, 'DATABASE_ERROR');
}

// External API error helper
export function externalApiError(message: string): AppError {
  return createError(message, 503, 'EXTERNAL_API_ERROR');
}
