# Use Bun.js official image
FROM oven/bun:1 as base

# Set working directory
WORKDIR /app

# Copy package files
COPY package.json bun.lockb ./
COPY shared/package.json ./shared/
COPY api/package.json ./api/

# Install dependencies
RUN bun install --frozen-lockfile

# Copy shared code
COPY shared/ ./shared/

# Copy API source code
COPY api/ ./api/

# Build shared package
WORKDIR /app/shared
RUN bun run build

# Build API
WORKDIR /app/api
RUN bun run build

# Production stage
FROM oven/bun:1-slim as production

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create app user
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Set working directory
WORKDIR /app

# Copy built application
COPY --from=base --chown=appuser:appuser /app/api/dist ./
COPY --from=base --chown=appuser:appuser /app/api/package.json ./
COPY --from=base --chown=appuser:appuser /app/node_modules ./node_modules

# Create necessary directories
RUN mkdir -p uploads sessions logs && \
    chown -R appuser:appuser uploads sessions logs

# Switch to app user
USER appuser

# Expose port
EXPOSE 3001

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3001/health || exit 1

# Start application
CMD ["bun", "run", "start"]
