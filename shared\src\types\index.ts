// User & Authentication Types
export interface User {
  id: string;
  username: string;
  email: string;
  fullName: string;
  avatar?: string;
  bio?: string;
  role: UserRole;
  plan: UserPlan;
  planExpiry?: Date;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export enum UserRole {
  SUPER_ADMIN = 'super_admin',
  ADMIN = 'admin',
  USER = 'user'
}

export enum UserPlan {
  FREE = 'free',
  STUDENT = 'student',
  HOBBY = 'hobby',
  BUGHUNTER = 'bughunter',
  CYBERSECURITY = 'cybersecurity'
}

// API Key Types
export interface ApiKey {
  id: string;
  userId: string;
  name: string;
  key: string;
  isActive: boolean;
  lastUsed?: Date;
  usageCount: number;
  rateLimit: number;
  createdAt: Date;
  expiresAt?: Date;
}

// OSINT Types
export interface OSINTRequest {
  type: OSINTType;
  query: string;
  options?: OSINTOptions;
}

export enum OSINTType {
  NAME = 'name',
  NIK = 'nik',
  NPWP = 'npwp',
  PHONE = 'phone',
  IMEI = 'imei',
  EMAIL = 'email',
  DOMAIN = 'domain'
}

export interface OSINTOptions {
  deepSearch?: boolean;
  includeLeaks?: boolean;
  includeGithub?: boolean;
  includeSocial?: boolean;
}

export interface OSINTResult {
  id: string;
  type: OSINTType;
  query: string;
  results: OSINTData[];
  confidence: number;
  sources: string[];
  timestamp: Date;
}

export interface OSINTData {
  source: string;
  data: Record<string, any>;
  confidence: number;
  verified: boolean;
}

// Vulnerability Scanner Types
export interface VulnScanRequest {
  target: string;
  scanTypes: VulnType[];
  options?: VulnScanOptions;
}

export enum VulnType {
  SQLI = 'sqli',
  XSS = 'xss',
  LFI = 'lfi',
  RCE = 'rce',
  PATH_TRAVERSAL = 'path_traversal',
  CSRF = 'csrf',
  SSRF = 'ssrf',
  XXE = 'xxe'
}

export interface VulnScanOptions {
  aggressive?: boolean;
  timeout?: number;
  userAgent?: string;
  headers?: Record<string, string>;
}

export interface VulnScanResult {
  id: string;
  target: string;
  vulnerabilities: Vulnerability[];
  scanDuration: number;
  timestamp: Date;
  status: ScanStatus;
}

export interface Vulnerability {
  type: VulnType;
  severity: VulnSeverity;
  title: string;
  description: string;
  url: string;
  payload?: string;
  cve?: string;
  cvss?: number;
  proof: string;
  recommendation: string;
}

export enum VulnSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export enum ScanStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

// File Analysis Types
export interface FileAnalysisRequest {
  fileName: string;
  fileContent: string | Buffer;
  fileType: string;
}

export interface FileAnalysisResult {
  id: string;
  fileName: string;
  fileType: string;
  fileSize: number;
  threats: FileThreat[];
  secrets: FileSecret[];
  hash: FileHash;
  timestamp: Date;
  status: AnalysisStatus;
}

export interface FileThreat {
  type: ThreatType;
  severity: VulnSeverity;
  description: string;
  location: string;
  confidence: number;
}

export enum ThreatType {
  WEBSHELL = 'webshell',
  RANSOMWARE = 'ransomware',
  DDOS_SCRIPT = 'ddos_script',
  MALWARE = 'malware',
  BACKDOOR = 'backdoor'
}

export interface FileSecret {
  type: SecretType;
  value: string;
  location: string;
  confidence: number;
}

export enum SecretType {
  API_KEY = 'api_key',
  PASSWORD = 'password',
  TOKEN = 'token',
  PRIVATE_KEY = 'private_key',
  DATABASE_URL = 'database_url'
}

export interface FileHash {
  md5: string;
  sha1: string;
  sha256: string;
  sha512: string;
}

export enum AnalysisStatus {
  PENDING = 'pending',
  ANALYZING = 'analyzing',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

// Bot Types
export interface BotConfig {
  id: string;
  type: BotType;
  name: string;
  token?: string;
  phoneNumber?: string;
  isActive: boolean;
  qrCode?: string;
  lastSeen?: Date;
  createdAt: Date;
}

export enum BotType {
  WHATSAPP = 'whatsapp',
  TELEGRAM = 'telegram'
}

// Plan & Payment Types
export interface Plan {
  id: string;
  name: string;
  type: UserPlan;
  price: number;
  duration: PlanDuration;
  features: PlanFeature[];
  limits: PlanLimits;
  isActive: boolean;
}

export enum PlanDuration {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  YEARLY = 'yearly'
}

export interface PlanFeature {
  name: string;
  description: string;
  enabled: boolean;
}

export interface PlanLimits {
  osintQueries: number;
  vulnScans: number;
  fileAnalysis: number;
  apiCalls: number;
  botAccess: boolean;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: Date;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Common Types
export interface BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}
