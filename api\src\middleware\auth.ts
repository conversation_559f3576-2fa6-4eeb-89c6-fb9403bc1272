import { Context, Next } from 'hono';
import { verify } from 'hono/jwt';
import { db } from '../database/connection';
import { users, apiKeys } from '../database/schema';
import { eq, and } from 'drizzle-orm';

export interface AuthUser {
  id: string;
  username: string;
  email: string;
  role: string;
  plan: string;
}

declare module 'hono' {
  interface ContextVariableMap {
    user: AuthUser;
    apiKey?: string;
  }
}

export async function authMiddleware(c: Context, next: Next) {
  try {
    const authHeader = c.req.header('Authorization');
    const apiKeyHeader = c.req.header('X-API-Key');

    let user: AuthUser | null = null;

    // Check for API Key authentication
    if (apiKeyHeader) {
      const [apiKeyResult] = await db
        .select({
          userId: apiKeys.userId,
          isActive: apiKeys.isActive
        })
        .from(apiKeys)
        .where(and(
          eq(apiKeys.key, apiKeyHeader),
          eq(apiKeys.isActive, true)
        ))
        .limit(1);

      if (!apiKeyResult) {
        return c.json({
          success: false,
          error: 'Invalid API key'
        }, 401);
      }

      // Get user details
      const [userResult] = await db
        .select({
          id: users.id,
          username: users.username,
          email: users.email,
          role: users.role,
          plan: users.plan,
          isActive: users.isActive
        })
        .from(users)
        .where(and(
          eq(users.id, apiKeyResult.userId),
          eq(users.isActive, true)
        ))
        .limit(1);

      if (!userResult) {
        return c.json({
          success: false,
          error: 'User not found or inactive'
        }, 401);
      }

      user = userResult;
      c.set('apiKey', apiKeyHeader);

      // Update API key usage
      await db
        .update(apiKeys)
        .set({
          lastUsed: new Date(),
          usageCount: apiKeys.usageCount + 1
        })
        .where(eq(apiKeys.key, apiKeyHeader));
    }
    // Check for JWT authentication
    else if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);

      try {
        const payload = await verify(token, process.env.JWT_SECRET!);
        
        // Get user details
        const [userResult] = await db
          .select({
            id: users.id,
            username: users.username,
            email: users.email,
            role: users.role,
            plan: users.plan,
            isActive: users.isActive
          })
          .from(users)
          .where(and(
            eq(users.id, payload.userId as string),
            eq(users.isActive, true)
          ))
          .limit(1);

        if (!userResult) {
          return c.json({
            success: false,
            error: 'User not found or inactive'
          }, 401);
        }

        user = userResult;
      } catch (error) {
        return c.json({
          success: false,
          error: 'Invalid or expired token'
        }, 401);
      }
    }

    if (!user) {
      return c.json({
        success: false,
        error: 'Authentication required'
      }, 401);
    }

    c.set('user', user);
    await next();
  } catch (error) {
    console.error('Auth middleware error:', error);
    return c.json({
      success: false,
      error: 'Authentication failed'
    }, 500);
  }
}

export function requireRole(roles: string[]) {
  return async (c: Context, next: Next) => {
    const user = c.get('user');
    
    if (!user || !roles.includes(user.role)) {
      return c.json({
        success: false,
        error: 'Insufficient permissions'
      }, 403);
    }

    await next();
  };
}

export function requirePlan(plans: string[]) {
  return async (c: Context, next: Next) => {
    const user = c.get('user');
    
    if (!user || !plans.includes(user.plan)) {
      return c.json({
        success: false,
        error: 'Plan upgrade required'
      }, 403);
    }

    await next();
  };
}
