import { Hono } from 'hono';
import { z<PERSON>alida<PERSON> } from '@hono/zod-validator';
import { z } from 'zod';
import { db } from '../database/connection';
import { botConfigs } from '../database/schema';
import { eq } from 'drizzle-orm';
import { generateId, BotType } from '@kodexguard/shared';
import { asyncHandler, validationError, notFoundError, forbiddenError } from '../middleware/error-handler';
import { requireRole } from '../middleware/auth';

const botRoutes = new Hono();

// Get bot status (all users can see)
botRoutes.get('/status', asyncHandler(async (c) => {
  const bots = await db
    .select({
      id: botConfigs.id,
      type: botConfigs.type,
      name: botConfigs.name,
      isActive: botConfigs.isActive,
      lastSeen: botConfigs.lastSeen
    })
    .from(botConfigs);

  return c.json({
    success: true,
    data: bots
  });
}));

// Get bot configurations (admin only)
botRoutes.get('/', requireRole(['super_admin', 'admin']), asyncHandler(async (c) => {
  const bots = await db
    .select()
    .from(botConfigs);

  return c.json({
    success: true,
    data: bots
  });
}));

// Create bot configuration (super admin only)
const createBotSchema = z.object({
  type: z.enum(['whatsapp', 'telegram']),
  name: z.string().min(1).max(100),
  token: z.string().optional(),
  phoneNumber: z.string().optional(),
  config: z.record(z.any()).optional()
});

botRoutes.post('/', requireRole(['super_admin']), zValidator('json', createBotSchema), asyncHandler(async (c) => {
  const { type, name, token, phoneNumber, config = {} } = c.req.valid('json');

  // Validate required fields based on bot type
  if (type === 'telegram' && !token) {
    throw validationError('Telegram bot requires token');
  }

  if (type === 'whatsapp' && !phoneNumber) {
    throw validationError('WhatsApp bot requires phone number');
  }

  const botId = generateId();
  await db.insert(botConfigs).values({
    id: botId,
    type,
    name,
    token,
    phoneNumber,
    isActive: false,
    config
  });

  return c.json({
    success: true,
    message: 'Bot configuration created successfully',
    data: { id: botId, type, name }
  }, 201);
}));

// Update bot configuration (super admin only)
const updateBotSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  token: z.string().optional(),
  phoneNumber: z.string().optional(),
  isActive: z.boolean().optional(),
  config: z.record(z.any()).optional()
});

botRoutes.put('/:id', requireRole(['super_admin']), zValidator('json', updateBotSchema), asyncHandler(async (c) => {
  const botId = c.req.param('id');
  const updateData = c.req.valid('json');

  const [existingBot] = await db
    .select({ id: botConfigs.id })
    .from(botConfigs)
    .where(eq(botConfigs.id, botId))
    .limit(1);

  if (!existingBot) {
    throw notFoundError('Bot configuration');
  }

  await db
    .update(botConfigs)
    .set({
      ...updateData,
      updatedAt: new Date()
    })
    .where(eq(botConfigs.id, botId));

  return c.json({
    success: true,
    message: 'Bot configuration updated successfully'
  });
}));

// Generate WhatsApp QR code (super admin only)
botRoutes.post('/:id/qr', requireRole(['super_admin']), asyncHandler(async (c) => {
  const botId = c.req.param('id');

  const [bot] = await db
    .select()
    .from(botConfigs)
    .where(eq(botConfigs.id, botId))
    .limit(1);

  if (!bot) {
    throw notFoundError('Bot configuration');
  }

  if (bot.type !== 'whatsapp') {
    throw validationError('QR code is only available for WhatsApp bots');
  }

  // Generate mock QR code (replace with actual venom.js implementation)
  const qrCode = `data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==`;

  await db
    .update(botConfigs)
    .set({
      qrCode,
      updatedAt: new Date()
    })
    .where(eq(botConfigs.id, botId));

  return c.json({
    success: true,
    message: 'QR code generated successfully',
    data: { qrCode }
  });
}));

// Delete bot configuration (super admin only)
botRoutes.delete('/:id', requireRole(['super_admin']), asyncHandler(async (c) => {
  const botId = c.req.param('id');

  const [existingBot] = await db
    .select({ id: botConfigs.id })
    .from(botConfigs)
    .where(eq(botConfigs.id, botId))
    .limit(1);

  if (!existingBot) {
    throw notFoundError('Bot configuration');
  }

  await db
    .delete(botConfigs)
    .where(eq(botConfigs.id, botId));

  return c.json({
    success: true,
    message: 'Bot configuration deleted successfully'
  });
}));

// Bot commands endpoint (for users to see available commands)
botRoutes.get('/commands', asyncHandler(async (c) => {
  const commands = {
    whatsapp: [
      { command: '/help', description: 'Show available commands' },
      { command: '/scan <url>', description: 'Start vulnerability scan' },
      { command: '/osint <type> <query>', description: 'Perform OSINT search' },
      { command: '/status', description: 'Check your plan status' },
      { command: '/hash <text>', description: 'Generate hash of text' },
      { command: '/encode <type> <text>', description: 'Encode text (base64, hex, rot13)' },
      { command: '/decode <type> <text>', description: 'Decode text (base64, hex, rot13)' }
    ],
    telegram: [
      { command: '/start', description: 'Start bot interaction' },
      { command: '/help', description: 'Show available commands' },
      { command: '/scan', description: 'Start vulnerability scan' },
      { command: '/osint', description: 'Perform OSINT search' },
      { command: '/status', description: 'Check your plan status' },
      { command: '/tools', description: 'Access security tools' },
      { command: '/settings', description: 'Bot settings' }
    ]
  };

  return c.json({
    success: true,
    data: commands
  });
}));

// Send message via bot (admin only)
const sendMessageSchema = z.object({
  botId: z.string(),
  recipient: z.string(),
  message: z.string().min(1).max(4000)
});

botRoutes.post('/send', requireRole(['super_admin', 'admin']), zValidator('json', sendMessageSchema), asyncHandler(async (c) => {
  const { botId, recipient, message } = c.req.valid('json');

  const [bot] = await db
    .select()
    .from(botConfigs)
    .where(eq(botConfigs.id, botId))
    .limit(1);

  if (!bot) {
    throw notFoundError('Bot configuration');
  }

  if (!bot.isActive) {
    throw validationError('Bot is not active');
  }

  // Mock message sending (replace with actual bot implementation)
  console.log(`Sending message via ${bot.type} bot to ${recipient}: ${message}`);

  return c.json({
    success: true,
    message: 'Message sent successfully',
    data: {
      botType: bot.type,
      recipient,
      messageLength: message.length
    }
  });
}));

// Get bot statistics (admin only)
botRoutes.get('/stats', requireRole(['super_admin', 'admin']), asyncHandler(async (c) => {
  const bots = await db
    .select()
    .from(botConfigs);

  const stats = {
    total: bots.length,
    active: bots.filter(bot => bot.isActive).length,
    inactive: bots.filter(bot => !bot.isActive).length,
    byType: {
      whatsapp: bots.filter(bot => bot.type === 'whatsapp').length,
      telegram: bots.filter(bot => bot.type === 'telegram').length
    },
    lastActivity: bots.reduce((latest, bot) => {
      if (!bot.lastSeen) return latest;
      return !latest || bot.lastSeen > latest ? bot.lastSeen : latest;
    }, null as Date | null)
  };

  return c.json({
    success: true,
    data: stats
  });
}));

export default botRoutes;
