'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import {
  HomeIcon,
  MagnifyingGlassIcon,
  ShieldCheckIcon,
  BugAntIcon,
  DocumentMagnifyingGlassIcon,
  WrenchScrewdriverIcon,
  CpuChipIcon,
  ChartBarIcon,
  UserIcon,
  CreditCardIcon,
  Cog6ToothIcon,
  Bars3Icon,
  XMarkIcon,
  BellIcon,
  MoonIcon,
  SunIcon,
} from '@heroicons/react/24/outline';
import { useAuth } from '@/lib/auth-context';
import { useTheme } from 'next-themes';

const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },
  { name: 'OSINT Investigasi', href: '/dashboard/osint', icon: MagnifyingGlassIcon },
  { name: 'Vulnerability Scanner', href: '/dashboard/vulnerability', icon: ShieldCheckIcon },
  { name: 'Exploit & CVE', href: '/dashboard/cve', icon: BugAntIcon },
  { name: 'File Analyzer', href: '/dashboard/file-analysis', icon: DocumentMagnifyingGlassIcon },
  { name: 'Dorking + CVE Harian', href: '/dashboard/dorking', icon: MagnifyingGlassIcon },
  { name: 'API Playground', href: '/dashboard/playground', icon: WrenchScrewdriverIcon },
  { name: 'Tools', href: '/dashboard/tools', icon: WrenchScrewdriverIcon },
  { name: 'Bot Center', href: '/dashboard/bots', icon: CpuChipIcon },
  { name: 'Leaderboard', href: '/dashboard/leaderboard', icon: ChartBarIcon },
  { name: 'Profil & API Key', href: '/dashboard/profile', icon: UserIcon },
  { name: 'Plan & Pembayaran', href: '/dashboard/plans', icon: CreditCardIcon },
];

const adminNavigation = [
  { name: 'Admin Panel', href: '/dashboard/admin', icon: Cog6ToothIcon },
];

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [mounted, setMounted] = useState(false);
  const pathname = usePathname();
  const { user, logout } = useAuth();
  const { theme, setTheme } = useTheme();

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  const isAdmin = user?.role === 'admin' || user?.role === 'super_admin';
  const allNavigation = isAdmin ? [...navigation, ...adminNavigation] : navigation;

  return (
    <div className="min-h-screen bg-dark-950">
      {/* Mobile sidebar */}
      <AnimatePresence>
        {sidebarOpen && (
          <>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 z-40 lg:hidden"
            >
              <div
                className="fixed inset-0 bg-dark-900/80 backdrop-blur-sm"
                onClick={() => setSidebarOpen(false)}
              />
            </motion.div>

            <motion.div
              initial={{ x: -300 }}
              animate={{ x: 0 }}
              exit={{ x: -300 }}
              transition={{ type: 'spring', damping: 30, stiffness: 300 }}
              className="fixed inset-y-0 left-0 z-50 w-64 bg-dark-900 border-r border-dark-700 lg:hidden"
            >
              <Sidebar navigation={allNavigation} pathname={pathname} user={user} />
            </motion.div>
          </>
        )}
      </AnimatePresence>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-64 lg:flex-col">
        <div className="bg-dark-900 border-r border-dark-700">
          <Sidebar navigation={allNavigation} pathname={pathname} user={user} />
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64">
        {/* Top bar */}
        <div className="sticky top-0 z-40 bg-dark-900/80 backdrop-blur-md border-b border-dark-700">
          <div className="flex h-16 items-center gap-x-4 px-4 sm:gap-x-6 sm:px-6 lg:px-8">
            <button
              type="button"
              className="-m-2.5 p-2.5 text-gray-400 lg:hidden"
              onClick={() => setSidebarOpen(true)}
            >
              <Bars3Icon className="h-6 w-6" />
            </button>

            <div className="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
              <div className="flex flex-1" />
              
              <div className="flex items-center gap-x-4 lg:gap-x-6">
                {/* Theme toggle */}
                <button
                  onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
                  className="p-2 text-gray-400 hover:text-white transition-colors"
                >
                  {theme === 'dark' ? (
                    <SunIcon className="h-5 w-5" />
                  ) : (
                    <MoonIcon className="h-5 w-5" />
                  )}
                </button>

                {/* Notifications */}
                <button className="p-2 text-gray-400 hover:text-white transition-colors">
                  <BellIcon className="h-5 w-5" />
                </button>

                {/* User menu */}
                <div className="flex items-center space-x-3">
                  <div className="text-right">
                    <p className="text-sm font-medium text-white">{user?.fullName}</p>
                    <p className="text-xs text-gray-400 capitalize">{user?.plan}</p>
                  </div>
                  <div className="w-8 h-8 bg-gradient-to-br from-neon-blue to-neon-purple rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium text-white">
                      {user?.fullName?.charAt(0).toUpperCase()}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="py-6">
          <div className="px-4 sm:px-6 lg:px-8">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}

function Sidebar({ navigation, pathname, user }: any) {
  const { logout } = useAuth();

  return (
    <div className="flex grow flex-col gap-y-5 overflow-y-auto px-6 py-4">
      {/* Logo */}
      <div className="flex h-16 shrink-0 items-center">
        <Link href="/dashboard" className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-gradient-to-br from-neon-blue to-neon-purple rounded-lg flex items-center justify-center">
            <ShieldCheckIcon className="w-5 h-5 text-white" />
          </div>
          <span className="text-xl font-display font-bold gradient-text">
            KodeXGuard
          </span>
        </Link>
      </div>

      {/* Navigation */}
      <nav className="flex flex-1 flex-col">
        <ul role="list" className="flex flex-1 flex-col gap-y-7">
          <li>
            <ul role="list" className="-mx-2 space-y-1">
              {navigation.map((item: any) => {
                const isActive = pathname === item.href || pathname.startsWith(item.href + '/');
                return (
                  <li key={item.name}>
                    <Link
                      href={item.href}
                      className={`
                        group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold transition-all duration-200
                        ${isActive
                          ? 'bg-neon-blue/10 text-neon-blue border-r-2 border-neon-blue'
                          : 'text-gray-400 hover:text-white hover:bg-dark-800'
                        }
                      `}
                    >
                      <item.icon
                        className={`h-5 w-5 shrink-0 ${
                          isActive ? 'text-neon-blue' : 'text-gray-400 group-hover:text-white'
                        }`}
                      />
                      {item.name}
                    </Link>
                  </li>
                );
              })}
            </ul>
          </li>

          {/* User info & logout */}
          <li className="mt-auto">
            <div className="p-4 bg-dark-800/50 rounded-lg border border-dark-600">
              <div className="flex items-center space-x-3 mb-3">
                <div className="w-10 h-10 bg-gradient-to-br from-neon-blue to-neon-purple rounded-full flex items-center justify-center">
                  <span className="text-sm font-medium text-white">
                    {user?.fullName?.charAt(0).toUpperCase()}
                  </span>
                </div>
                <div>
                  <p className="text-sm font-medium text-white">{user?.fullName}</p>
                  <p className="text-xs text-gray-400">@{user?.username}</p>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className={`text-xs px-2 py-1 rounded-full plan-${user?.plan}`}>
                  {user?.plan?.toUpperCase()}
                </span>
                <button
                  onClick={logout}
                  className="text-xs text-gray-400 hover:text-red-400 transition-colors"
                >
                  Logout
                </button>
              </div>
            </div>
          </li>
        </ul>
      </nav>
    </div>
  );
}
