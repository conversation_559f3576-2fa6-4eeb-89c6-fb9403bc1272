# 📦 Installation Guide - KodeXGuard

Panduan lengkap instalasi dan setup KodeXGuard platform.

## 🔧 Prerequisites

### System Requirements
- **OS**: Linux, macOS, atau Windows 10/11
- **RAM**: Minimum 4GB, Recommended 8GB+
- **Storage**: Minimum 2GB free space
- **Network**: Internet connection untuk download dependencies

### Software Requirements
- **Node.js**: Version 18.0.0 atau lebih baru
- **Bun.js**: Latest version
- **MySQL**: Version 8.0 atau lebih baru
- **Git**: Latest version

### Optional (untuk Mobile Development)
- **Android Studio**: Untuk Android development
- **Xcode**: Untuk iOS development (macOS only)
- **Expo CLI**: Untuk React Native development

## 🚀 Quick Installation

### 1. Clone Repository
```bash
git clone https://github.com/kodexguard/kodexguard.git
cd kodexguard
```

### 2. Install Bun.js (jika belum ada)
```bash
# Linux/macOS
curl -fsSL https://bun.sh/install | bash

# Windows (PowerShell)
powershell -c "irm bun.sh/install.ps1 | iex"

# Restart terminal setelah instalasi
```

### 3. Install Dependencies
```bash
# Install root dependencies
bun install

# Install workspace dependencies
bun install --workspaces
```

### 4. Setup Database
```bash
# Login ke MySQL
mysql -u root -p

# Buat database
CREATE DATABASE kodexguard;
CREATE USER 'kodexguard'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON kodexguard.* TO 'kodexguard'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 5. Environment Configuration
```bash
# Copy environment files
cp api/.env.example api/.env
cp web/.env.example web/.env.local

# Edit API environment
nano api/.env
```

**api/.env**:
```env
# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=kodexguard
DB_PASSWORD=your_password
DB_NAME=kodexguard

# JWT Secret (generate random string)
JWT_SECRET=your_super_secret_jwt_key_here

# Server Configuration
PORT=3001
NODE_ENV=development

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# Rate Limiting
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_WINDOW_MS=900000

# File Upload
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=text/plain,application/javascript,application/php

# Bot Configuration (optional)
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
WHATSAPP_SESSION_PATH=./sessions/whatsapp

# Payment Gateways (optional)
TRIPAY_API_KEY=your_tripay_api_key
TRIPAY_PRIVATE_KEY=your_tripay_private_key
MIDTRANS_SERVER_KEY=your_midtrans_server_key
XENDIT_SECRET_KEY=your_xendit_secret_key
```

**web/.env.local**:
```env
NEXT_PUBLIC_API_URL=http://localhost:3001
NEXT_PUBLIC_WS_URL=ws://localhost:3001
NEXT_PUBLIC_APP_NAME=KodeXGuard
NEXT_PUBLIC_APP_VERSION=1.0.0
```

### 6. Database Migration
```bash
cd api

# Generate database schema
bun run db:generate

# Run migrations
bun run db:migrate

# Seed initial data
bun run db:seed
```

### 7. Start Development Servers
```bash
# Terminal 1: Start API server
cd api
bun run dev

# Terminal 2: Start web application
cd web
bun run dev

# Terminal 3: Start mobile app (optional)
cd mobile
bun run start
```

## 🌐 Access Applications

- **Web Application**: http://localhost:3000
- **API Server**: http://localhost:3001
- **API Documentation**: http://localhost:3001/docs
- **Mobile App**: Scan QR code dengan Expo Go

## 📱 Mobile App Setup

### Prerequisites
```bash
# Install Expo CLI globally
npm install -g @expo/cli

# Install EAS CLI (untuk build production)
npm install -g eas-cli
```

### Development Setup
```bash
cd mobile

# Install dependencies
bun install

# Start development server
bun run start

# Untuk platform specific
bun run android  # Android
bun run ios      # iOS
bun run web      # Web
```

### Production Build
```bash
# Login ke Expo account
eas login

# Configure project
eas build:configure

# Build for Android
eas build --platform android

# Build for iOS
eas build --platform ios

# Build for both platforms
eas build --platform all
```

## 🔧 Advanced Configuration

### SSL/HTTPS Setup (Production)
```bash
# Generate SSL certificate
openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365

# Update API configuration
# Add to api/.env
HTTPS_ENABLED=true
SSL_KEY_PATH=./ssl/key.pem
SSL_CERT_PATH=./ssl/cert.pem
```

### Docker Setup (Optional)
```bash
# Build Docker images
docker-compose build

# Start services
docker-compose up -d

# View logs
docker-compose logs -f
```

### Nginx Reverse Proxy (Production)
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
    
    location /api {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## 🐛 Troubleshooting

### Common Issues

**1. Database Connection Error**
```bash
# Check MySQL service
sudo systemctl status mysql

# Restart MySQL
sudo systemctl restart mysql

# Check connection
mysql -u kodexguard -p -h localhost
```

**2. Port Already in Use**
```bash
# Check what's using port 3000/3001
lsof -i :3000
lsof -i :3001

# Kill process
kill -9 <PID>
```

**3. Permission Errors**
```bash
# Fix npm permissions
sudo chown -R $(whoami) ~/.npm
sudo chown -R $(whoami) ~/.bun
```

**4. Module Not Found**
```bash
# Clear cache and reinstall
rm -rf node_modules
rm bun.lockb
bun install
```

### Performance Optimization

**1. Database Optimization**
```sql
-- Add indexes for better performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_osint_results_user_id ON osint_results(userId);
CREATE INDEX idx_vuln_scans_user_id ON vuln_scans(userId);
```

**2. API Optimization**
```bash
# Enable production mode
export NODE_ENV=production

# Use PM2 for process management
npm install -g pm2
pm2 start api/src/index.ts --name kodexguard-api
```

## 📊 Monitoring & Logging

### Setup Logging
```bash
# Create logs directory
mkdir -p logs

# Add to api/.env
LOG_LEVEL=info
LOG_FILE=./logs/app.log
```

### Health Checks
```bash
# API health check
curl http://localhost:3001/health

# Database health check
curl http://localhost:3001/health/db
```

## 🔐 Security Considerations

### Production Security
1. **Change default passwords**
2. **Use strong JWT secrets**
3. **Enable HTTPS**
4. **Configure firewall**
5. **Regular security updates**
6. **Database backup strategy**

### Environment Security
```bash
# Set proper file permissions
chmod 600 api/.env
chmod 600 web/.env.local

# Use environment-specific configurations
# Never commit .env files to git
```

## 📞 Support

Jika mengalami masalah instalasi:

- **Documentation**: [docs.kodexguard.com](https://docs.kodexguard.com)
- **GitHub Issues**: [github.com/kodexguard/kodexguard/issues](https://github.com/kodexguard/kodexguard/issues)
- **Discord**: [discord.gg/kodexguard](https://discord.gg/kodexguard)
- **Email**: <EMAIL>

---

**Selamat! KodeXGuard sudah siap digunakan! 🎉**
