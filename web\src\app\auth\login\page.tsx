'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { EyeIcon, EyeSlashIcon, ShieldCheckIcon } from '@heroicons/react/24/outline';
import { useAuth } from '@/lib/auth-context';

interface LoginForm {
  identifier: string;
  password: string;
}

export default function LoginPage() {
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const { login } = useAuth();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginForm>();

  const onSubmit = async (data: LoginForm) => {
    setLoading(true);
    try {
      await login(data.identifier, data.password);
    } catch (error) {
      // Error handled by auth context
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {/* Header */}
          <div className="text-center">
            <Link href="/" className="flex justify-center items-center space-x-2 mb-6">
              <div className="w-12 h-12 bg-gradient-to-br from-neon-blue to-neon-purple rounded-lg flex items-center justify-center">
                <ShieldCheckIcon className="w-7 h-7 text-white" />
              </div>
              <span className="text-2xl font-display font-bold gradient-text">
                KodeXGuard
              </span>
            </Link>
            <h2 className="text-3xl font-bold text-white mb-2">
              Masuk ke Akun Anda
            </h2>
            <p className="text-gray-400">
              Lanjutkan perjalanan cybersecurity Anda
            </p>
          </div>

          {/* Form */}
          <form className="mt-8 space-y-6" onSubmit={handleSubmit(onSubmit)}>
            <div className="space-y-4">
              {/* Username/Email */}
              <div>
                <label htmlFor="identifier" className="block text-sm font-medium text-gray-300 mb-2">
                  Username atau Email
                </label>
                <input
                  {...register('identifier', {
                    required: 'Username atau email wajib diisi',
                  })}
                  type="text"
                  className="input-cyber w-full"
                  placeholder="Masukkan username atau email"
                />
                {errors.identifier && (
                  <p className="mt-1 text-sm text-red-400">{errors.identifier.message}</p>
                )}
              </div>

              {/* Password */}
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-300 mb-2">
                  Password
                </label>
                <div className="relative">
                  <input
                    {...register('password', {
                      required: 'Password wajib diisi',
                    })}
                    type={showPassword ? 'text' : 'password'}
                    className="input-cyber w-full pr-10"
                    placeholder="Masukkan password"
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeSlashIcon className="h-5 w-5 text-gray-400" />
                    ) : (
                      <EyeIcon className="h-5 w-5 text-gray-400" />
                    )}
                  </button>
                </div>
                {errors.password && (
                  <p className="mt-1 text-sm text-red-400">{errors.password.message}</p>
                )}
              </div>
            </div>

            {/* Remember & Forgot */}
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  id="remember-me"
                  name="remember-me"
                  type="checkbox"
                  className="h-4 w-4 text-neon-blue focus:ring-neon-blue border-gray-600 rounded bg-dark-800"
                />
                <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-300">
                  Ingat saya
                </label>
              </div>

              <div className="text-sm">
                <Link
                  href="/auth/forgot-password"
                  className="text-neon-blue hover:text-neon-blue/80 transition-colors"
                >
                  Lupa password?
                </Link>
              </div>
            </div>

            {/* Submit Button */}
            <div>
              <button
                type="submit"
                disabled={loading}
                className="btn-neon w-full flex justify-center items-center"
              >
                {loading ? (
                  <div className="spinner mr-2" />
                ) : null}
                {loading ? 'Masuk...' : 'Masuk'}
              </button>
            </div>

            {/* Register Link */}
            <div className="text-center">
              <p className="text-gray-400">
                Belum punya akun?{' '}
                <Link
                  href="/auth/register"
                  className="text-neon-blue hover:text-neon-blue/80 transition-colors font-medium"
                >
                  Daftar sekarang
                </Link>
              </p>
            </div>
          </form>

          {/* Demo Account */}
          <div className="mt-6 p-4 bg-dark-800/50 rounded-lg border border-dark-600">
            <h3 className="text-sm font-medium text-gray-300 mb-2">Demo Account</h3>
            <div className="text-xs text-gray-400 space-y-1">
              <p><strong>Username:</strong> demo</p>
              <p><strong>Password:</strong> demo123</p>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
