import { UserPlan, VulnType, OSINTType, ThreatType, SecretType } from '../types';

// Application Constants
export const APP_NAME = 'KodeXGuard';
export const APP_VERSION = '1.0.0';
export const APP_DESCRIPTION = 'Platform mandiri cybersecurity & bug hunting Indonesia';

// API Constants
export const API_VERSION = 'v1';
export const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3001';
export const WEB_BASE_URL = process.env.WEB_BASE_URL || 'http://localhost:3000';

// Authentication Constants
export const JWT_EXPIRES_IN = '7d';
export const API_KEY_LENGTH = 32;
export const PASSWORD_MIN_LENGTH = 8;
export const USERNAME_MIN_LENGTH = 3;
export const USERNAME_MAX_LENGTH = 30;

// Rate Limiting
export const RATE_LIMITS = {
  [UserPlan.FREE]: {
    osintQueries: 10,
    vulnScans: 5,
    fileAnalysis: 3,
    apiCalls: 100
  },
  [UserPlan.STUDENT]: {
    osintQueries: 50,
    vulnScans: 25,
    fileAnalysis: 15,
    apiCalls: 500
  },
  [UserPlan.HOBBY]: {
    osintQueries: 100,
    vulnScans: 50,
    fileAnalysis: 30,
    apiCalls: 1000
  },
  [UserPlan.BUGHUNTER]: {
    osintQueries: 500,
    vulnScans: 200,
    fileAnalysis: 100,
    apiCalls: 5000
  },
  [UserPlan.CYBERSECURITY]: {
    osintQueries: -1, // unlimited
    vulnScans: -1,
    fileAnalysis: -1,
    apiCalls: -1
  }
} as const;

// Plan Pricing (in IDR)
export const PLAN_PRICING = {
  [UserPlan.FREE]: {
    daily: 0,
    weekly: 0,
    monthly: 0,
    yearly: 0
  },
  [UserPlan.STUDENT]: {
    daily: 2500,
    weekly: 15000,
    monthly: 25000,
    yearly: 250000
  },
  [UserPlan.HOBBY]: {
    daily: 5000,
    weekly: 30000,
    monthly: 50000,
    yearly: 500000
  },
  [UserPlan.BUGHUNTER]: {
    daily: 10000,
    weekly: 60000,
    monthly: 100000,
    yearly: 1000000
  },
  [UserPlan.CYBERSECURITY]: {
    daily: 20000,
    weekly: 120000,
    monthly: 200000,
    yearly: 2000000
  }
} as const;

// Vulnerability Scanner Constants
export const VULN_PAYLOADS = {
  [VulnType.SQLI]: [
    "' OR '1'='1",
    "' UNION SELECT NULL--",
    "'; DROP TABLE users--",
    "' OR 1=1#",
    "admin'--"
  ],
  [VulnType.XSS]: [
    "<script>alert('XSS')</script>",
    "<img src=x onerror=alert('XSS')>",
    "javascript:alert('XSS')",
    "<svg onload=alert('XSS')>",
    "';alert('XSS');//"
  ],
  [VulnType.LFI]: [
    "../../../etc/passwd",
    "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
    "....//....//....//etc/passwd",
    "php://filter/read=convert.base64-encode/resource=index.php"
  ],
  [VulnType.RCE]: [
    "; ls -la",
    "| whoami",
    "`id`",
    "$(whoami)",
    "&& dir"
  ]
} as const;

// OSINT Data Sources
export const OSINT_SOURCES = {
  [OSINTType.NAME]: [
    'dukcapil_leaked',
    'kemkes_leaked',
    'social_media',
    'public_records'
  ],
  [OSINTType.NIK]: [
    'dukcapil_leaked',
    'kemkes_leaked',
    'bpjs_leaked'
  ],
  [OSINTType.PHONE]: [
    'truecaller',
    'getcontact',
    'social_media',
    'leaked_databases'
  ],
  [OSINTType.EMAIL]: [
    'haveibeenpwned',
    'github_commits',
    'social_media',
    'leaked_databases'
  ],
  [OSINTType.DOMAIN]: [
    'whois',
    'dns_records',
    'subdomain_enum',
    'certificate_transparency'
  ]
} as const;

// File Analysis Constants
export const THREAT_SIGNATURES = {
  [ThreatType.WEBSHELL]: [
    'eval(',
    'base64_decode(',
    'system(',
    'exec(',
    'shell_exec(',
    'passthru(',
    'file_get_contents('
  ],
  [ThreatType.RANSOMWARE]: [
    'CryptEncrypt',
    'CryptDecrypt',
    'ransom',
    'encrypt_files',
    'decrypt_files'
  ],
  [ThreatType.DDOS_SCRIPT]: [
    'socket_create',
    'fsockopen',
    'curl_multi',
    'flood',
    'ddos'
  ]
} as const;

export const SECRET_PATTERNS = {
  [SecretType.API_KEY]: [
    /api[_-]?key[_-]?[=:]\s*['"]?([a-zA-Z0-9]{20,})/i,
    /key[_-]?[=:]\s*['"]?([a-zA-Z0-9]{20,})/i
  ],
  [SecretType.PASSWORD]: [
    /password[_-]?[=:]\s*['"]?([^\s'"]{8,})/i,
    /pwd[_-]?[=:]\s*['"]?([^\s'"]{8,})/i
  ],
  [SecretType.TOKEN]: [
    /token[_-]?[=:]\s*['"]?([a-zA-Z0-9._-]{20,})/i,
    /bearer[_-]?\s+([a-zA-Z0-9._-]{20,})/i
  ],
  [SecretType.DATABASE_URL]: [
    /(mysql|postgresql|mongodb):\/\/[^\s'"]+/i,
    /database[_-]?url[_-]?[=:]\s*['"]?([^\s'"]+)/i
  ]
} as const;

// Google Dorking Presets
export const DORK_PRESETS = [
  'site:pastebin.com "password"',
  'site:github.com "api_key"',
  'filetype:sql "INSERT INTO" password',
  'intitle:"index of" "config.php"',
  'inurl:admin intitle:login',
  'site:*.id filetype:pdf',
  'intext:"sql syntax near" | intext:"syntax error has occurred"',
  'intext:"Warning: mysql_connect()"',
  'intitle:"phpMyAdmin" "Welcome to phpMyAdmin"',
  'inurl:"/phpmyadmin/index.php"'
] as const;

// CVE Severity Mapping
export const CVE_SEVERITY_MAPPING = {
  'LOW': { min: 0.1, max: 3.9, color: '#28a745' },
  'MEDIUM': { min: 4.0, max: 6.9, color: '#ffc107' },
  'HIGH': { min: 7.0, max: 8.9, color: '#fd7e14' },
  'CRITICAL': { min: 9.0, max: 10.0, color: '#dc3545' }
} as const;

// Bot Commands
export const BOT_COMMANDS = {
  HELP: '/help',
  SCAN: '/scan',
  STATUS: '/status',
  PLAN: '/plan',
  OSINT: '/osint',
  VULN: '/vuln',
  FILE: '/file',
  HASH: '/hash'
} as const;

// File Upload Limits
export const FILE_UPLOAD_LIMITS = {
  MAX_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_TYPES: [
    'text/plain',
    'application/javascript',
    'text/html',
    'application/php',
    'text/x-python',
    'application/zip',
    'application/x-rar-compressed'
  ]
} as const;

// Regex Patterns
export const REGEX_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^(\+62|62|0)[0-9]{9,13}$/,
  NIK: /^[0-9]{16}$/,
  NPWP: /^[0-9]{15}$/,
  IMEI: /^[0-9]{15}$/,
  USERNAME: /^[a-zA-Z0-9_]{3,30}$/,
  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  UNAUTHORIZED: 'Unauthorized access',
  FORBIDDEN: 'Access forbidden',
  NOT_FOUND: 'Resource not found',
  VALIDATION_ERROR: 'Validation error',
  RATE_LIMIT_EXCEEDED: 'Rate limit exceeded',
  PLAN_LIMIT_EXCEEDED: 'Plan limit exceeded',
  INVALID_API_KEY: 'Invalid API key',
  EXPIRED_TOKEN: 'Token expired',
  INVALID_CREDENTIALS: 'Invalid credentials',
  USER_NOT_FOUND: 'User not found',
  EMAIL_ALREADY_EXISTS: 'Email already exists',
  USERNAME_ALREADY_EXISTS: 'Username already exists'
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  USER_CREATED: 'User created successfully',
  USER_UPDATED: 'User updated successfully',
  LOGIN_SUCCESS: 'Login successful',
  LOGOUT_SUCCESS: 'Logout successful',
  SCAN_STARTED: 'Scan started successfully',
  SCAN_COMPLETED: 'Scan completed successfully',
  FILE_UPLOADED: 'File uploaded successfully',
  API_KEY_CREATED: 'API key created successfully',
  PLAN_UPDATED: 'Plan updated successfully'
} as const;
