import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import { db } from '../database/connection';
import { cveDatabase } from '../database/schema';
import { eq, like, and, gte, lte, desc } from 'drizzle-orm';
import { asyncHandler, notFoundError } from '../middleware/error-handler';

const cveRoutes = new Hono();

// Get CVE list with filters
const cveListSchema = z.object({
  page: z.string().optional(),
  limit: z.string().optional(),
  severity: z.enum(['low', 'medium', 'high', 'critical']).optional(),
  year: z.string().optional(),
  search: z.string().optional(),
  cvssMin: z.string().optional(),
  cvssMax: z.string().optional(),
  hasExploit: z.string().optional()
});

cveRoutes.get('/', zValidator('query', cveListSchema), asyncHandler(async (c) => {
  const {
    page = '1',
    limit = '20',
    severity,
    year,
    search,
    cvssMin,
    cvssMax,
    hasExploit
  } = c.req.valid('query');

  const pageNum = parseInt(page);
  const limitNum = parseInt(limit);
  const offset = (pageNum - 1) * limitNum;

  // Build where conditions
  const conditions = [];

  if (severity) {
    conditions.push(eq(cveDatabase.severity, severity));
  }

  if (year) {
    const startDate = new Date(`${year}-01-01`);
    const endDate = new Date(`${year}-12-31`);
    conditions.push(
      and(
        gte(cveDatabase.publishedDate, startDate),
        lte(cveDatabase.publishedDate, endDate)
      )
    );
  }

  if (search) {
    conditions.push(
      like(cveDatabase.description, `%${search}%`)
    );
  }

  if (cvssMin) {
    conditions.push(gte(cveDatabase.cvssScore, parseFloat(cvssMin)));
  }

  if (cvssMax) {
    conditions.push(lte(cveDatabase.cvssScore, parseFloat(cvssMax)));
  }

  if (hasExploit === 'true') {
    conditions.push(eq(cveDatabase.exploitAvailable, true));
  }

  const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

  const cves = await db
    .select({
      id: cveDatabase.id,
      cveId: cveDatabase.cveId,
      description: cveDatabase.description,
      severity: cveDatabase.severity,
      cvssScore: cveDatabase.cvssScore,
      publishedDate: cveDatabase.publishedDate,
      exploitAvailable: cveDatabase.exploitAvailable
    })
    .from(cveDatabase)
    .where(whereClause)
    .orderBy(desc(cveDatabase.publishedDate))
    .limit(limitNum)
    .offset(offset);

  return c.json({
    success: true,
    data: cves,
    pagination: {
      page: pageNum,
      limit: limitNum,
      total: cves.length
    }
  });
}));

// Get CVE details
cveRoutes.get('/:cveId', asyncHandler(async (c) => {
  const cveId = c.req.param('cveId');

  const [cve] = await db
    .select()
    .from(cveDatabase)
    .where(eq(cveDatabase.cveId, cveId))
    .limit(1);

  if (!cve) {
    throw notFoundError('CVE');
  }

  return c.json({
    success: true,
    data: cve
  });
}));

// Get CVE statistics
cveRoutes.get('/stats/overview', asyncHandler(async (c) => {
  const allCves = await db
    .select({
      severity: cveDatabase.severity,
      exploitAvailable: cveDatabase.exploitAvailable,
      publishedDate: cveDatabase.publishedDate
    })
    .from(cveDatabase);

  const currentYear = new Date().getFullYear();
  const thisYearCves = allCves.filter(cve => 
    cve.publishedDate && cve.publishedDate.getFullYear() === currentYear
  );

  const stats = {
    total: allCves.length,
    thisYear: thisYearCves.length,
    withExploits: allCves.filter(cve => cve.exploitAvailable).length,
    bySeverity: {
      critical: allCves.filter(cve => cve.severity === 'critical').length,
      high: allCves.filter(cve => cve.severity === 'high').length,
      medium: allCves.filter(cve => cve.severity === 'medium').length,
      low: allCves.filter(cve => cve.severity === 'low').length
    },
    thisYearBySeverity: {
      critical: thisYearCves.filter(cve => cve.severity === 'critical').length,
      high: thisYearCves.filter(cve => cve.severity === 'high').length,
      medium: thisYearCves.filter(cve => cve.severity === 'medium').length,
      low: thisYearCves.filter(cve => cve.severity === 'low').length
    }
  };

  return c.json({
    success: true,
    data: stats
  });
}));

// Get recent CVEs
cveRoutes.get('/recent/list', asyncHandler(async (c) => {
  const limit = parseInt(c.req.query('limit') || '10');

  const recentCves = await db
    .select({
      id: cveDatabase.id,
      cveId: cveDatabase.cveId,
      description: cveDatabase.description,
      severity: cveDatabase.severity,
      cvssScore: cveDatabase.cvssScore,
      publishedDate: cveDatabase.publishedDate,
      exploitAvailable: cveDatabase.exploitAvailable
    })
    .from(cveDatabase)
    .orderBy(desc(cveDatabase.publishedDate))
    .limit(limit);

  return c.json({
    success: true,
    data: recentCves
  });
}));

// Get critical CVEs
cveRoutes.get('/critical/list', asyncHandler(async (c) => {
  const limit = parseInt(c.req.query('limit') || '10');

  const criticalCves = await db
    .select({
      id: cveDatabase.id,
      cveId: cveDatabase.cveId,
      description: cveDatabase.description,
      severity: cveDatabase.severity,
      cvssScore: cveDatabase.cvssScore,
      publishedDate: cveDatabase.publishedDate,
      exploitAvailable: cveDatabase.exploitAvailable
    })
    .from(cveDatabase)
    .where(eq(cveDatabase.severity, 'critical'))
    .orderBy(desc(cveDatabase.publishedDate))
    .limit(limit);

  return c.json({
    success: true,
    data: criticalCves
  });
}));

// Get CVEs with exploits
cveRoutes.get('/exploits/available', asyncHandler(async (c) => {
  const limit = parseInt(c.req.query('limit') || '10');

  const exploitCves = await db
    .select({
      id: cveDatabase.id,
      cveId: cveDatabase.cveId,
      description: cveDatabase.description,
      severity: cveDatabase.severity,
      cvssScore: cveDatabase.cvssScore,
      publishedDate: cveDatabase.publishedDate,
      exploitAvailable: cveDatabase.exploitAvailable
    })
    .from(cveDatabase)
    .where(eq(cveDatabase.exploitAvailable, true))
    .orderBy(desc(cveDatabase.publishedDate))
    .limit(limit);

  return c.json({
    success: true,
    data: exploitCves
  });
}));

// Search CVEs
const searchSchema = z.object({
  query: z.string().min(1),
  limit: z.string().optional()
});

cveRoutes.post('/search', zValidator('json', searchSchema), asyncHandler(async (c) => {
  const { query, limit = '20' } = c.req.valid('json');
  const limitNum = parseInt(limit);

  const searchResults = await db
    .select({
      id: cveDatabase.id,
      cveId: cveDatabase.cveId,
      description: cveDatabase.description,
      severity: cveDatabase.severity,
      cvssScore: cveDatabase.cvssScore,
      publishedDate: cveDatabase.publishedDate,
      exploitAvailable: cveDatabase.exploitAvailable
    })
    .from(cveDatabase)
    .where(
      like(cveDatabase.description, `%${query}%`)
    )
    .orderBy(desc(cveDatabase.publishedDate))
    .limit(limitNum);

  return c.json({
    success: true,
    data: searchResults,
    query,
    count: searchResults.length
  });
}));

// Get CVE trends (monthly data for charts)
cveRoutes.get('/trends/monthly', asyncHandler(async (c) => {
  const year = parseInt(c.req.query('year') || new Date().getFullYear().toString());

  // Get all CVEs for the specified year
  const startDate = new Date(`${year}-01-01`);
  const endDate = new Date(`${year}-12-31`);

  const yearCves = await db
    .select({
      publishedDate: cveDatabase.publishedDate,
      severity: cveDatabase.severity
    })
    .from(cveDatabase)
    .where(
      and(
        gte(cveDatabase.publishedDate, startDate),
        lte(cveDatabase.publishedDate, endDate)
      )
    );

  // Group by month
  const monthlyData = Array.from({ length: 12 }, (_, i) => ({
    month: i + 1,
    monthName: new Date(year, i).toLocaleString('default', { month: 'long' }),
    total: 0,
    critical: 0,
    high: 0,
    medium: 0,
    low: 0
  }));

  yearCves.forEach(cve => {
    if (cve.publishedDate) {
      const month = cve.publishedDate.getMonth();
      monthlyData[month].total++;
      monthlyData[month][cve.severity as keyof typeof monthlyData[0]]++;
    }
  });

  return c.json({
    success: true,
    data: {
      year,
      months: monthlyData,
      totalYear: yearCves.length
    }
  });
}));

export default cveRoutes;
