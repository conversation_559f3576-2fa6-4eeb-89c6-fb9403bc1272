import crypto from 'crypto';
import { REGEX_PATTERNS } from '../constants';
import { VulnSeverity, UserPlan } from '../types';

// Validation Utilities
export const validateEmail = (email: string): boolean => {
  return REGEX_PATTERNS.EMAIL.test(email);
};

export const validatePhone = (phone: string): boolean => {
  return REGEX_PATTERNS.PHONE.test(phone);
};

export const validateNIK = (nik: string): boolean => {
  return REGEX_PATTERNS.NIK.test(nik);
};

export const validateNPWP = (npwp: string): boolean => {
  return REGEX_PATTERNS.NPWP.test(npwp);
};

export const validateIMEI = (imei: string): boolean => {
  return REGEX_PATTERNS.IMEI.test(imei);
};

export const validateUsername = (username: string): boolean => {
  return REGEX_PATTERNS.USERNAME.test(username);
};

export const validatePassword = (password: string): boolean => {
  return REGEX_PATTERNS.PASSWORD.test(password);
};

// Cryptography Utilities
export const generateApiKey = (): string => {
  return crypto.randomBytes(32).toString('hex');
};

export const generateSecretKey = (): string => {
  return crypto.randomBytes(64).toString('hex');
};

export const hashPassword = (password: string): string => {
  const salt = crypto.randomBytes(16).toString('hex');
  const hash = crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex');
  return `${salt}:${hash}`;
};

export const verifyPassword = (password: string, hashedPassword: string): boolean => {
  const [salt, hash] = hashedPassword.split(':');
  const verifyHash = crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex');
  return hash === verifyHash;
};

export const generateHash = {
  md5: (data: string): string => crypto.createHash('md5').update(data).digest('hex'),
  sha1: (data: string): string => crypto.createHash('sha1').update(data).digest('hex'),
  sha256: (data: string): string => crypto.createHash('sha256').update(data).digest('hex'),
  sha512: (data: string): string => crypto.createHash('sha512').update(data).digest('hex')
};

// Encoding/Decoding Utilities
export const encodeBase64 = (data: string): string => {
  return Buffer.from(data, 'utf8').toString('base64');
};

export const decodeBase64 = (data: string): string => {
  return Buffer.from(data, 'base64').toString('utf8');
};

export const encodeHex = (data: string): string => {
  return Buffer.from(data, 'utf8').toString('hex');
};

export const decodeHex = (data: string): string => {
  return Buffer.from(data, 'hex').toString('utf8');
};

export const rot13 = (data: string): string => {
  return data.replace(/[a-zA-Z]/g, (char) => {
    const start = char <= 'Z' ? 65 : 97;
    return String.fromCharCode(((char.charCodeAt(0) - start + 13) % 26) + start);
  });
};

// URL Utilities
export const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

export const extractDomain = (url: string): string => {
  try {
    return new URL(url).hostname;
  } catch {
    return '';
  }
};

export const normalizeUrl = (url: string): string => {
  if (!url.startsWith('http://') && !url.startsWith('https://')) {
    return `https://${url}`;
  }
  return url;
};

// Date Utilities
export const formatDate = (date: Date): string => {
  return date.toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

export const addDays = (date: Date, days: number): Date => {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
};

export const isExpired = (date: Date): boolean => {
  return new Date() > date;
};

// String Utilities
export const slugify = (text: string): string => {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '');
};

export const truncate = (text: string, length: number): string => {
  if (text.length <= length) return text;
  return text.substring(0, length) + '...';
};

export const capitalize = (text: string): string => {
  return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
};

export const generateId = (): string => {
  return crypto.randomUUID();
};

// Security Utilities
export const sanitizeInput = (input: string): string => {
  return input
    .replace(/[<>]/g, '')
    .replace(/javascript:/gi, '')
    .replace(/on\w+=/gi, '')
    .trim();
};

export const maskSensitiveData = (data: string, visibleChars: number = 4): string => {
  if (data.length <= visibleChars * 2) return data;
  const start = data.substring(0, visibleChars);
  const end = data.substring(data.length - visibleChars);
  const masked = '*'.repeat(data.length - visibleChars * 2);
  return `${start}${masked}${end}`;
};

// Vulnerability Utilities
export const calculateCVSS = (baseScore: number): VulnSeverity => {
  if (baseScore >= 9.0) return VulnSeverity.CRITICAL;
  if (baseScore >= 7.0) return VulnSeverity.HIGH;
  if (baseScore >= 4.0) return VulnSeverity.MEDIUM;
  return VulnSeverity.LOW;
};

export const getSeverityColor = (severity: VulnSeverity): string => {
  const colors = {
    [VulnSeverity.LOW]: '#28a745',
    [VulnSeverity.MEDIUM]: '#ffc107',
    [VulnSeverity.HIGH]: '#fd7e14',
    [VulnSeverity.CRITICAL]: '#dc3545'
  };
  return colors[severity];
};

// Plan Utilities
export const getPlanLimits = (plan: UserPlan) => {
  const limits = {
    [UserPlan.FREE]: { osint: 10, vuln: 5, file: 3, api: 100 },
    [UserPlan.STUDENT]: { osint: 50, vuln: 25, file: 15, api: 500 },
    [UserPlan.HOBBY]: { osint: 100, vuln: 50, file: 30, api: 1000 },
    [UserPlan.BUGHUNTER]: { osint: 500, vuln: 200, file: 100, api: 5000 },
    [UserPlan.CYBERSECURITY]: { osint: -1, vuln: -1, file: -1, api: -1 }
  };
  return limits[plan];
};

export const isUnlimited = (limit: number): boolean => {
  return limit === -1;
};

// File Utilities
export const getFileExtension = (filename: string): string => {
  return filename.split('.').pop()?.toLowerCase() || '';
};

export const formatFileSize = (bytes: number): string => {
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  if (bytes === 0) return '0 Bytes';
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
};

export const isAllowedFileType = (mimeType: string, allowedTypes: string[]): boolean => {
  return allowedTypes.includes(mimeType);
};

// Array Utilities
export const chunk = <T>(array: T[], size: number): T[][] => {
  const chunks: T[][] = [];
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size));
  }
  return chunks;
};

export const unique = <T>(array: T[]): T[] => {
  return [...new Set(array)];
};

export const shuffle = <T>(array: T[]): T[] => {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
};

// Rate Limiting Utilities
export const createRateLimitKey = (userId: string, action: string): string => {
  return `rate_limit:${userId}:${action}`;
};

export const calculateResetTime = (windowMs: number): Date => {
  return new Date(Date.now() + windowMs);
};

// Error Handling Utilities
export const createError = (message: string, code?: string, statusCode?: number) => {
  const error = new Error(message) as any;
  error.code = code;
  error.statusCode = statusCode;
  return error;
};

export const isValidationError = (error: any): boolean => {
  return error.name === 'ValidationError' || error.code === 'VALIDATION_ERROR';
};

// Logging Utilities
export const createLogEntry = (level: string, message: string, meta?: any) => {
  return {
    timestamp: new Date().toISOString(),
    level,
    message,
    meta
  };
};

export const redactSensitiveFields = (obj: any): any => {
  const sensitiveFields = ['password', 'token', 'apiKey', 'secret'];
  const redacted = { ...obj };
  
  for (const field of sensitiveFields) {
    if (redacted[field]) {
      redacted[field] = '[REDACTED]';
    }
  }
  
  return redacted;
};
