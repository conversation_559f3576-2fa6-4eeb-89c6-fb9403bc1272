import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import * as SecureStore from 'expo-secure-store';
import { api } from './api';
import { useDatabase } from './database-context';
import { useNetwork } from './network-context';
import Toast from 'react-native-toast-message';

interface User {
  id: string;
  username: string;
  email: string;
  fullName: string;
  avatar?: string;
  role: string;
  plan: string;
  planExpiry?: string;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (identifier: string, password: string) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
  updateUser: (data: Partial<User>) => void;
  syncUserData: () => Promise<void>;
}

interface RegisterData {
  username: string;
  email: string;
  password: string;
  fullName: string;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const { isConnected } = useNetwork();
  const { saveUser, getUser, clearUser } = useDatabase();

  useEffect(() => {
    checkAuth();
  }, []);

  useEffect(() => {
    // Sync user data when coming online
    if (isConnected && user) {
      syncUserData();
    }
  }, [isConnected]);

  const checkAuth = async () => {
    try {
      // First check local storage
      const localUser = await getUser();
      if (localUser) {
        setUser(localUser);
      }

      // Then check secure store for token
      const token = await SecureStore.getItemAsync('auth-token');
      if (!token) {
        setLoading(false);
        return;
      }

      // Verify token with server if online
      if (isConnected) {
        try {
          const response = await api.get('/auth/verify');
          if (response.data.success) {
            const serverUser = response.data.data.user;
            setUser(serverUser);
            await saveUser(serverUser);
          } else {
            await SecureStore.deleteItemAsync('auth-token');
            await clearUser();
            setUser(null);
          }
        } catch (error) {
          console.error('Auth verification failed:', error);
          // Keep local user if server is unreachable
        }
      }
    } catch (error) {
      console.error('Auth check failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const login = async (identifier: string, password: string) => {
    try {
      const response = await api.post('/auth/login', {
        identifier,
        password,
      });

      if (response.data.success) {
        const { user: userData, token } = response.data.data;
        
        // Store token securely
        await SecureStore.setItemAsync('auth-token', token);
        
        // Save user data locally
        await saveUser(userData);
        
        setUser(userData);
        
        Toast.show({
          type: 'success',
          text1: 'Login Berhasil',
          text2: 'Selamat datang kembali!',
        });
      } else {
        throw new Error(response.data.error || 'Login gagal');
      }
    } catch (error: any) {
      const message = error.response?.data?.error || error.message || 'Login gagal';
      Toast.show({
        type: 'error',
        text1: 'Login Gagal',
        text2: message,
      });
      throw error;
    }
  };

  const register = async (data: RegisterData) => {
    try {
      const response = await api.post('/auth/register', data);

      if (response.data.success) {
        const { user: userData, token } = response.data.data;
        
        // Store token securely
        await SecureStore.setItemAsync('auth-token', token);
        
        // Save user data locally
        await saveUser(userData);
        
        setUser(userData);
        
        Toast.show({
          type: 'success',
          text1: 'Registrasi Berhasil',
          text2: 'Selamat datang di KodeXGuard!',
        });
      } else {
        throw new Error(response.data.error || 'Registrasi gagal');
      }
    } catch (error: any) {
      const message = error.response?.data?.error || error.message || 'Registrasi gagal';
      Toast.show({
        type: 'error',
        text1: 'Registrasi Gagal',
        text2: message,
      });
      throw error;
    }
  };

  const logout = async () => {
    try {
      // Clear secure storage
      await SecureStore.deleteItemAsync('auth-token');
      
      // Clear local database
      await clearUser();
      
      setUser(null);
      
      Toast.show({
        type: 'success',
        text1: 'Logout Berhasil',
        text2: 'Sampai jumpa lagi!',
      });
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const updateUser = async (data: Partial<User>) => {
    if (user) {
      const updatedUser = { ...user, ...data };
      setUser(updatedUser);
      await saveUser(updatedUser);
    }
  };

  const syncUserData = async () => {
    if (!isConnected || !user) return;

    try {
      const response = await api.get('/users/profile');
      if (response.data.success) {
        const serverUser = response.data.data;
        setUser(serverUser);
        await saveUser(serverUser);
      }
    } catch (error) {
      console.error('User sync failed:', error);
    }
  };

  const value = {
    user,
    loading,
    login,
    register,
    logout,
    updateUser,
    syncUserData,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
