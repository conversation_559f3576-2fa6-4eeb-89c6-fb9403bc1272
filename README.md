# 🛡️ KodeXGuard - Cybersecurity & Bug Hunting Platform

KodeXGuard adalah platform mandiri untuk cybersecurity dan bug hunting yang menggabungkan fitur OSINT, Vulnerability Scanner, Bot Automation, File Analyzer, Developer Playground, CVE Intelligence, dan komunitas hunter.

## 🌟 Fitur Utama

- 🔍 **OSINT Investigator** - Pencarian nama, NIK, NPWP, nomor HP, IMEI, email, domain
- 🛡️ **Vulnerability Scanner** - Deteksi SQLi, XSS, LFI, RCE, Path Traversal, CSRF
- 💣 **Exploit & CVE** - Database CVE terbaru dengan exploit tools
- 📂 **File Analyzer** - Deteksi webshell, ransomware, DDoS script, secret/token
- 🔍 **Google Dorking** - Preset dan custom dork dengan CVE harian
- 🤖 **Bot WhatsApp & Telegram** - Automation dan notifikasi
- 🧪 **Developer Playground** - API testing dan dokumentasi
- ⚙️ **Security Tools** - Hash, encode, payload generator
- 📊 **Leaderboard** - Komunitas bug hunter Indonesia
- 💼 **Sistem Plan** - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ughunter, Cybersecurity

## 🏗️ Arsitektur

```
kodexguard/
├── web/                    # Next.js 14 Web Application
├── api/                    # Bun.js Backend API
├── mobile/                 # React Native Mobile App
├── shared/                 # Shared utilities & types
├── docs/                   # Documentation & API specs
└── scripts/                # Deployment & utility scripts
```

## 🚀 Tech Stack

- **Frontend**: Next.js 14, TypeScript, TailwindCSS
- **Backend**: Bun.js, RESTful API, JWT Authentication
- **Database**: MySQL (production), SQLite (mobile offline)
- **Mobile**: React Native, Expo, SQLite
- **Bot**: venom.js (WhatsApp), Telegram Bot API
- **Payment**: Tripay, Midtrans, Xendit

## 📦 Installation

### Prerequisites
- Node.js 18+
- Bun.js
- MySQL 8.0+
- React Native CLI / Expo CLI

### Setup
```bash
# Clone repository
git clone https://github.com/yourusername/kodexguard.git
cd kodexguard

# Install dependencies
bun install

# Setup database
cd api && bun run db:setup

# Start development servers
bun run dev:all
```

## 🔧 Development

```bash
# Start web app
cd web && bun run dev

# Start API server
cd api && bun run dev

# Start mobile app
cd mobile && expo start
```

## 📚 Documentation

- [API Documentation](./docs/api.md)
- [User Guide](./docs/user-guide.md)
- [Developer Guide](./docs/developer-guide.md)
- [Bot Setup Guide](./docs/bot-setup.md)

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🛡️ Security

For security issues, <NAME_EMAIL>

## 📞 Support

- Website: https://kodexguard.com
- Email: <EMAIL>
- Telegram: @kodexguard_support
- WhatsApp: +62-xxx-xxxx-xxxx

---

**KodeXGuard** - Empowering Indonesian Cybersecurity Community 🇮🇩
