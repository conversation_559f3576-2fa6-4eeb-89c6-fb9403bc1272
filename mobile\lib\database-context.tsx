import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import * as SQLite from 'expo-sqlite';
import { generateId } from '@kodexguard/shared';

interface DatabaseContextType {
  db: SQLite.SQLiteDatabase | null;
  isReady: boolean;
  saveUser: (user: any) => Promise<void>;
  getUser: () => Promise<any>;
  clearUser: () => Promise<void>;
  saveOSINTResult: (result: any) => Promise<void>;
  getOSINTResults: () => Promise<any[]>;
  saveVulnScan: (scan: any) => Promise<void>;
  getVulnScans: () => Promise<any[]>;
  saveFileAnalysis: (analysis: any) => Promise<void>;
  getFileAnalyses: () => Promise<any[]>;
  saveTool: (tool: any) => Promise<void>;
  getTools: () => Promise<any[]>;
  clearAllData: () => Promise<void>;
}

const DatabaseContext = createContext<DatabaseContextType | undefined>(undefined);

export function DatabaseProvider({ children }: { children: ReactNode }) {
  const [db, setDb] = useState<SQLite.SQLiteDatabase | null>(null);
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    initDatabase();
  }, []);

  const initDatabase = async () => {
    try {
      const database = await SQLite.openDatabaseAsync('kodexguard.db');
      
      // Create tables
      await database.execAsync(`
        PRAGMA journal_mode = WAL;
        
        -- Users table
        CREATE TABLE IF NOT EXISTS users (
          id TEXT PRIMARY KEY,
          username TEXT UNIQUE,
          email TEXT UNIQUE,
          fullName TEXT,
          avatar TEXT,
          role TEXT,
          plan TEXT,
          planExpiry TEXT,
          createdAt TEXT,
          updatedAt TEXT
        );

        -- OSINT Results table
        CREATE TABLE IF NOT EXISTS osint_results (
          id TEXT PRIMARY KEY,
          userId TEXT,
          type TEXT,
          query TEXT,
          results TEXT,
          confidence REAL,
          sources TEXT,
          status TEXT,
          createdAt TEXT,
          FOREIGN KEY (userId) REFERENCES users (id)
        );

        -- Vulnerability Scans table
        CREATE TABLE IF NOT EXISTS vuln_scans (
          id TEXT PRIMARY KEY,
          userId TEXT,
          target TEXT,
          scanTypes TEXT,
          vulnerabilities TEXT,
          scanDuration INTEGER,
          status TEXT,
          createdAt TEXT,
          completedAt TEXT,
          FOREIGN KEY (userId) REFERENCES users (id)
        );

        -- File Analysis table
        CREATE TABLE IF NOT EXISTS file_analysis (
          id TEXT PRIMARY KEY,
          userId TEXT,
          fileName TEXT,
          fileType TEXT,
          fileSize INTEGER,
          threats TEXT,
          secrets TEXT,
          hash TEXT,
          status TEXT,
          createdAt TEXT,
          completedAt TEXT,
          FOREIGN KEY (userId) REFERENCES users (id)
        );

        -- Tools table (for offline tools)
        CREATE TABLE IF NOT EXISTS tools (
          id TEXT PRIMARY KEY,
          userId TEXT,
          type TEXT,
          input TEXT,
          output TEXT,
          createdAt TEXT,
          FOREIGN KEY (userId) REFERENCES users (id)
        );

        -- Sync Queue table
        CREATE TABLE IF NOT EXISTS sync_queue (
          id TEXT PRIMARY KEY,
          table_name TEXT,
          record_id TEXT,
          action TEXT,
          data TEXT,
          createdAt TEXT
        );
      `);

      setDb(database);
      setIsReady(true);
      console.log('Database initialized successfully');
    } catch (error) {
      console.error('Database initialization failed:', error);
    }
  };

  // User operations
  const saveUser = async (user: any) => {
    if (!db) return;
    
    try {
      await db.runAsync(
        `INSERT OR REPLACE INTO users 
         (id, username, email, fullName, avatar, role, plan, planExpiry, createdAt, updatedAt) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          user.id,
          user.username,
          user.email,
          user.fullName,
          user.avatar || null,
          user.role,
          user.plan,
          user.planExpiry || null,
          user.createdAt || new Date().toISOString(),
          new Date().toISOString()
        ]
      );
    } catch (error) {
      console.error('Save user failed:', error);
    }
  };

  const getUser = async () => {
    if (!db) return null;
    
    try {
      const result = await db.getFirstAsync('SELECT * FROM users LIMIT 1');
      return result;
    } catch (error) {
      console.error('Get user failed:', error);
      return null;
    }
  };

  const clearUser = async () => {
    if (!db) return;
    
    try {
      await db.runAsync('DELETE FROM users');
    } catch (error) {
      console.error('Clear user failed:', error);
    }
  };

  // OSINT operations
  const saveOSINTResult = async (result: any) => {
    if (!db) return;
    
    try {
      const id = result.id || generateId();
      await db.runAsync(
        `INSERT OR REPLACE INTO osint_results 
         (id, userId, type, query, results, confidence, sources, status, createdAt) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          id,
          result.userId,
          result.type,
          result.query,
          JSON.stringify(result.results || []),
          result.confidence || 0,
          JSON.stringify(result.sources || []),
          result.status,
          result.createdAt || new Date().toISOString()
        ]
      );
    } catch (error) {
      console.error('Save OSINT result failed:', error);
    }
  };

  const getOSINTResults = async () => {
    if (!db) return [];
    
    try {
      const results = await db.getAllAsync(
        'SELECT * FROM osint_results ORDER BY createdAt DESC'
      );
      return results.map((result: any) => ({
        ...result,
        results: JSON.parse(result.results || '[]'),
        sources: JSON.parse(result.sources || '[]')
      }));
    } catch (error) {
      console.error('Get OSINT results failed:', error);
      return [];
    }
  };

  // Vulnerability Scan operations
  const saveVulnScan = async (scan: any) => {
    if (!db) return;
    
    try {
      const id = scan.id || generateId();
      await db.runAsync(
        `INSERT OR REPLACE INTO vuln_scans 
         (id, userId, target, scanTypes, vulnerabilities, scanDuration, status, createdAt, completedAt) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          id,
          scan.userId,
          scan.target,
          JSON.stringify(scan.scanTypes || []),
          JSON.stringify(scan.vulnerabilities || []),
          scan.scanDuration || 0,
          scan.status,
          scan.createdAt || new Date().toISOString(),
          scan.completedAt || null
        ]
      );
    } catch (error) {
      console.error('Save vuln scan failed:', error);
    }
  };

  const getVulnScans = async () => {
    if (!db) return [];
    
    try {
      const results = await db.getAllAsync(
        'SELECT * FROM vuln_scans ORDER BY createdAt DESC'
      );
      return results.map((result: any) => ({
        ...result,
        scanTypes: JSON.parse(result.scanTypes || '[]'),
        vulnerabilities: JSON.parse(result.vulnerabilities || '[]')
      }));
    } catch (error) {
      console.error('Get vuln scans failed:', error);
      return [];
    }
  };

  // File Analysis operations
  const saveFileAnalysis = async (analysis: any) => {
    if (!db) return;
    
    try {
      const id = analysis.id || generateId();
      await db.runAsync(
        `INSERT OR REPLACE INTO file_analysis 
         (id, userId, fileName, fileType, fileSize, threats, secrets, hash, status, createdAt, completedAt) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          id,
          analysis.userId,
          analysis.fileName,
          analysis.fileType,
          analysis.fileSize || 0,
          JSON.stringify(analysis.threats || []),
          JSON.stringify(analysis.secrets || []),
          JSON.stringify(analysis.hash || {}),
          analysis.status,
          analysis.createdAt || new Date().toISOString(),
          analysis.completedAt || null
        ]
      );
    } catch (error) {
      console.error('Save file analysis failed:', error);
    }
  };

  const getFileAnalyses = async () => {
    if (!db) return [];
    
    try {
      const results = await db.getAllAsync(
        'SELECT * FROM file_analysis ORDER BY createdAt DESC'
      );
      return results.map((result: any) => ({
        ...result,
        threats: JSON.parse(result.threats || '[]'),
        secrets: JSON.parse(result.secrets || '[]'),
        hash: JSON.parse(result.hash || '{}')
      }));
    } catch (error) {
      console.error('Get file analyses failed:', error);
      return [];
    }
  };

  // Tools operations
  const saveTool = async (tool: any) => {
    if (!db) return;
    
    try {
      const id = tool.id || generateId();
      await db.runAsync(
        `INSERT INTO tools (id, userId, type, input, output, createdAt) 
         VALUES (?, ?, ?, ?, ?, ?)`,
        [
          id,
          tool.userId,
          tool.type,
          tool.input,
          tool.output,
          new Date().toISOString()
        ]
      );
    } catch (error) {
      console.error('Save tool failed:', error);
    }
  };

  const getTools = async () => {
    if (!db) return [];
    
    try {
      const results = await db.getAllAsync(
        'SELECT * FROM tools ORDER BY createdAt DESC LIMIT 100'
      );
      return results;
    } catch (error) {
      console.error('Get tools failed:', error);
      return [];
    }
  };

  // Clear all data
  const clearAllData = async () => {
    if (!db) return;
    
    try {
      await db.execAsync(`
        DELETE FROM users;
        DELETE FROM osint_results;
        DELETE FROM vuln_scans;
        DELETE FROM file_analysis;
        DELETE FROM tools;
        DELETE FROM sync_queue;
      `);
    } catch (error) {
      console.error('Clear all data failed:', error);
    }
  };

  const value = {
    db,
    isReady,
    saveUser,
    getUser,
    clearUser,
    saveOSINTResult,
    getOSINTResults,
    saveVulnScan,
    getVulnScans,
    saveFileAnalysis,
    getFileAnalyses,
    saveTool,
    getTools,
    clearAllData,
  };

  return (
    <DatabaseContext.Provider value={value}>
      {children}
    </DatabaseContext.Provider>
  );
}

export function useDatabase() {
  const context = useContext(DatabaseContext);
  if (context === undefined) {
    throw new Error('useDatabase must be used within a DatabaseProvider');
  }
  return context;
}
