{"name": "@kodexguard/web", "version": "1.0.0", "description": "KodeXGuard Web Application", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@kodexguard/shared": "workspace:*", "next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "@next/font": "^14.0.4", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "clsx": "^2.0.0", "framer-motion": "^10.16.16", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-query": "^3.39.3", "axios": "^1.6.2", "js-cookie": "^3.0.5", "react-syntax-highlighter": "^15.5.0", "react-chartjs-2": "^5.2.0", "chart.js": "^4.4.0", "date-fns": "^2.30.0", "react-dropzone": "^14.2.3", "react-markdown": "^9.0.1", "prismjs": "^1.29.0", "lucide-react": "^0.294.0"}, "devDependencies": {"@types/node": "^20.10.0", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "@types/js-cookie": "^3.0.6", "@types/react-syntax-highlighter": "^15.5.11", "typescript": "^5.3.0", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0"}}