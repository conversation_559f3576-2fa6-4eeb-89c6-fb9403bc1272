'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { EyeIcon, EyeSlashIcon, ShieldCheckIcon, CheckCircleIcon } from '@heroicons/react/24/outline';
import { useAuth } from '@/lib/auth-context';

interface RegisterForm {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
  fullName: string;
  terms: boolean;
}

export default function RegisterPage() {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const { register: registerUser } = useAuth();

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<RegisterForm>();

  const password = watch('password');

  const onSubmit = async (data: RegisterForm) => {
    setLoading(true);
    try {
      await registerUser({
        username: data.username,
        email: data.email,
        password: data.password,
        fullName: data.fullName,
      });
    } catch (error) {
      // Error handled by auth context
    } finally {
      setLoading(false);
    }
  };

  const validatePassword = (value: string) => {
    if (value.length < 8) return 'Password minimal 8 karakter';
    if (!/(?=.*[a-z])/.test(value)) return 'Password harus mengandung huruf kecil';
    if (!/(?=.*[A-Z])/.test(value)) return 'Password harus mengandung huruf besar';
    if (!/(?=.*\d)/.test(value)) return 'Password harus mengandung angka';
    return true;
  };

  return (
    <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {/* Header */}
          <div className="text-center">
            <Link href="/" className="flex justify-center items-center space-x-2 mb-6">
              <div className="w-12 h-12 bg-gradient-to-br from-neon-blue to-neon-purple rounded-lg flex items-center justify-center">
                <ShieldCheckIcon className="w-7 h-7 text-white" />
              </div>
              <span className="text-2xl font-display font-bold gradient-text">
                KodeXGuard
              </span>
            </Link>
            <h2 className="text-3xl font-bold text-white mb-2">
              Bergabung dengan KodeXGuard
            </h2>
            <p className="text-gray-400">
              Mulai perjalanan cybersecurity Anda hari ini
            </p>
          </div>

          {/* Form */}
          <form className="mt-8 space-y-6" onSubmit={handleSubmit(onSubmit)}>
            <div className="space-y-4">
              {/* Full Name */}
              <div>
                <label htmlFor="fullName" className="block text-sm font-medium text-gray-300 mb-2">
                  Nama Lengkap
                </label>
                <input
                  {...register('fullName', {
                    required: 'Nama lengkap wajib diisi',
                    minLength: {
                      value: 2,
                      message: 'Nama lengkap minimal 2 karakter',
                    },
                  })}
                  type="text"
                  className="input-cyber w-full"
                  placeholder="Masukkan nama lengkap"
                />
                {errors.fullName && (
                  <p className="mt-1 text-sm text-red-400">{errors.fullName.message}</p>
                )}
              </div>

              {/* Username */}
              <div>
                <label htmlFor="username" className="block text-sm font-medium text-gray-300 mb-2">
                  Username
                </label>
                <input
                  {...register('username', {
                    required: 'Username wajib diisi',
                    minLength: {
                      value: 3,
                      message: 'Username minimal 3 karakter',
                    },
                    maxLength: {
                      value: 30,
                      message: 'Username maksimal 30 karakter',
                    },
                    pattern: {
                      value: /^[a-zA-Z0-9_]+$/,
                      message: 'Username hanya boleh mengandung huruf, angka, dan underscore',
                    },
                  })}
                  type="text"
                  className="input-cyber w-full"
                  placeholder="Masukkan username"
                />
                {errors.username && (
                  <p className="mt-1 text-sm text-red-400">{errors.username.message}</p>
                )}
              </div>

              {/* Email */}
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2">
                  Email
                </label>
                <input
                  {...register('email', {
                    required: 'Email wajib diisi',
                    pattern: {
                      value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                      message: 'Format email tidak valid',
                    },
                  })}
                  type="email"
                  className="input-cyber w-full"
                  placeholder="Masukkan email"
                />
                {errors.email && (
                  <p className="mt-1 text-sm text-red-400">{errors.email.message}</p>
                )}
              </div>

              {/* Password */}
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-300 mb-2">
                  Password
                </label>
                <div className="relative">
                  <input
                    {...register('password', {
                      required: 'Password wajib diisi',
                      validate: validatePassword,
                    })}
                    type={showPassword ? 'text' : 'password'}
                    className="input-cyber w-full pr-10"
                    placeholder="Masukkan password"
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeSlashIcon className="h-5 w-5 text-gray-400" />
                    ) : (
                      <EyeIcon className="h-5 w-5 text-gray-400" />
                    )}
                  </button>
                </div>
                {errors.password && (
                  <p className="mt-1 text-sm text-red-400">{errors.password.message}</p>
                )}
                
                {/* Password Requirements */}
                <div className="mt-2 space-y-1">
                  <div className="flex items-center text-xs">
                    <CheckCircleIcon 
                      className={`w-4 h-4 mr-2 ${
                        password && password.length >= 8 ? 'text-green-400' : 'text-gray-500'
                      }`} 
                    />
                    <span className={password && password.length >= 8 ? 'text-green-400' : 'text-gray-500'}>
                      Minimal 8 karakter
                    </span>
                  </div>
                  <div className="flex items-center text-xs">
                    <CheckCircleIcon 
                      className={`w-4 h-4 mr-2 ${
                        password && /(?=.*[a-z])(?=.*[A-Z])/.test(password) ? 'text-green-400' : 'text-gray-500'
                      }`} 
                    />
                    <span className={password && /(?=.*[a-z])(?=.*[A-Z])/.test(password) ? 'text-green-400' : 'text-gray-500'}>
                      Huruf besar dan kecil
                    </span>
                  </div>
                  <div className="flex items-center text-xs">
                    <CheckCircleIcon 
                      className={`w-4 h-4 mr-2 ${
                        password && /(?=.*\d)/.test(password) ? 'text-green-400' : 'text-gray-500'
                      }`} 
                    />
                    <span className={password && /(?=.*\d)/.test(password) ? 'text-green-400' : 'text-gray-500'}>
                      Mengandung angka
                    </span>
                  </div>
                </div>
              </div>

              {/* Confirm Password */}
              <div>
                <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-300 mb-2">
                  Konfirmasi Password
                </label>
                <div className="relative">
                  <input
                    {...register('confirmPassword', {
                      required: 'Konfirmasi password wajib diisi',
                      validate: (value) =>
                        value === password || 'Password tidak cocok',
                    })}
                    type={showConfirmPassword ? 'text' : 'password'}
                    className="input-cyber w-full pr-10"
                    placeholder="Konfirmasi password"
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? (
                      <EyeSlashIcon className="h-5 w-5 text-gray-400" />
                    ) : (
                      <EyeIcon className="h-5 w-5 text-gray-400" />
                    )}
                  </button>
                </div>
                {errors.confirmPassword && (
                  <p className="mt-1 text-sm text-red-400">{errors.confirmPassword.message}</p>
                )}
              </div>

              {/* Terms */}
              <div className="flex items-start">
                <input
                  {...register('terms', {
                    required: 'Anda harus menyetujui syarat dan ketentuan',
                  })}
                  id="terms"
                  type="checkbox"
                  className="h-4 w-4 text-neon-blue focus:ring-neon-blue border-gray-600 rounded bg-dark-800 mt-1"
                />
                <label htmlFor="terms" className="ml-2 block text-sm text-gray-300">
                  Saya menyetujui{' '}
                  <Link href="/terms" className="text-neon-blue hover:text-neon-blue/80">
                    Syarat dan Ketentuan
                  </Link>{' '}
                  serta{' '}
                  <Link href="/privacy" className="text-neon-blue hover:text-neon-blue/80">
                    Kebijakan Privasi
                  </Link>
                </label>
              </div>
              {errors.terms && (
                <p className="mt-1 text-sm text-red-400">{errors.terms.message}</p>
              )}
            </div>

            {/* Submit Button */}
            <div>
              <button
                type="submit"
                disabled={loading}
                className="btn-neon w-full flex justify-center items-center"
              >
                {loading ? (
                  <div className="spinner mr-2" />
                ) : null}
                {loading ? 'Mendaftar...' : 'Daftar Sekarang'}
              </button>
            </div>

            {/* Login Link */}
            <div className="text-center">
              <p className="text-gray-400">
                Sudah punya akun?{' '}
                <Link
                  href="/auth/login"
                  className="text-neon-blue hover:text-neon-blue/80 transition-colors font-medium"
                >
                  Masuk di sini
                </Link>
              </p>
            </div>
          </form>
        </motion.div>
      </div>
    </div>
  );
}
