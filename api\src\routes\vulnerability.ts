import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import { db } from '../database/connection';
import { vulnScans } from '../database/schema';
import { eq } from 'drizzle-orm';
import { generateId, VulnType, isValidUrl, normalizeUrl } from '@kodexguard/shared';
import { asyncHandler, validationError, notFoundError } from '../middleware/error-handler';
import { planRateLimiter } from '../middleware/rate-limiter';

const vulnRoutes = new Hono();

// Apply plan-based rate limiting
vulnRoutes.use('*', planRateLimiter({
  free: 5,
  student: 25,
  hobby: 50,
  bughunter: 200,
  cybersecurity: -1
}));

// Vulnerability scan schema
const vulnScanSchema = z.object({
  target: z.string().min(1).max(255),
  scanTypes: z.array(z.enum(['sqli', 'xss', 'lfi', 'rce', 'path_traversal', 'csrf', 'ssrf', 'xxe'])),
  options: z.object({
    aggressive: z.boolean().optional(),
    timeout: z.number().min(1).max(300).optional(),
    userAgent: z.string().max(255).optional(),
    headers: z.record(z.string()).optional()
  }).optional()
});

// Start vulnerability scan
vulnRoutes.post('/scan', zValidator('json', vulnScanSchema), asyncHandler(async (c) => {
  const user = c.get('user');
  const { target, scanTypes, options = {} } = c.req.valid('json');

  // Validate target URL
  const normalizedTarget = normalizeUrl(target);
  if (!isValidUrl(normalizedTarget)) {
    throw validationError('Invalid target URL');
  }

  // Validate scan types
  if (scanTypes.length === 0) {
    throw validationError('At least one scan type must be selected');
  }

  // Create scan record
  const scanId = generateId();
  await db.insert(vulnScans).values({
    id: scanId,
    userId: user.id,
    target: normalizedTarget,
    scanTypes: scanTypes,
    vulnerabilities: [],
    status: 'pending'
  });

  // Start vulnerability scan (async)
  performVulnerabilityScans(scanId, normalizedTarget, scanTypes as VulnType[], options);

  return c.json({
    success: true,
    message: 'Vulnerability scan started',
    data: {
      scanId,
      target: normalizedTarget,
      scanTypes,
      status: 'pending'
    }
  }, 202);
}));

// Get scan result
vulnRoutes.get('/scan/:id', asyncHandler(async (c) => {
  const user = c.get('user');
  const scanId = c.req.param('id');

  const [scan] = await db
    .select()
    .from(vulnScans)
    .where(eq(vulnScans.id, scanId))
    .limit(1);

  if (!scan || scan.userId !== user.id) {
    throw notFoundError('Vulnerability scan');
  }

  return c.json({
    success: true,
    data: scan
  });
}));

// Get scan history
vulnRoutes.get('/history', asyncHandler(async (c) => {
  const user = c.get('user');
  const page = parseInt(c.req.query('page') || '1');
  const limit = parseInt(c.req.query('limit') || '20');
  const offset = (page - 1) * limit;

  const scans = await db
    .select({
      id: vulnScans.id,
      target: vulnScans.target,
      scanTypes: vulnScans.scanTypes,
      status: vulnScans.status,
      scanDuration: vulnScans.scanDuration,
      createdAt: vulnScans.createdAt,
      completedAt: vulnScans.completedAt
    })
    .from(vulnScans)
    .where(eq(vulnScans.userId, user.id))
    .orderBy(vulnScans.createdAt)
    .limit(limit)
    .offset(offset);

  return c.json({
    success: true,
    data: scans,
    pagination: {
      page,
      limit,
      total: scans.length
    }
  });
}));

// Get vulnerability statistics
vulnRoutes.get('/stats', asyncHandler(async (c) => {
  const user = c.get('user');

  // Get user's vulnerability statistics
  const userScans = await db
    .select()
    .from(vulnScans)
    .where(eq(vulnScans.userId, user.id));

  const stats = {
    totalScans: userScans.length,
    completedScans: userScans.filter(scan => scan.status === 'completed').length,
    totalVulnerabilities: userScans.reduce((total, scan) => {
      return total + (Array.isArray(scan.vulnerabilities) ? scan.vulnerabilities.length : 0);
    }, 0),
    severityBreakdown: {
      critical: 0,
      high: 0,
      medium: 0,
      low: 0
    },
    typeBreakdown: {
      sqli: 0,
      xss: 0,
      lfi: 0,
      rce: 0,
      path_traversal: 0,
      csrf: 0,
      ssrf: 0,
      xxe: 0
    }
  };

  // Calculate severity and type breakdown
  userScans.forEach(scan => {
    if (Array.isArray(scan.vulnerabilities)) {
      scan.vulnerabilities.forEach((vuln: any) => {
        if (vuln.severity) {
          stats.severityBreakdown[vuln.severity]++;
        }
        if (vuln.type) {
          stats.typeBreakdown[vuln.type]++;
        }
      });
    }
  });

  return c.json({
    success: true,
    data: stats
  });
}));

// Get available scan types and payloads
vulnRoutes.get('/scan-types', asyncHandler(async (c) => {
  const scanTypes = {
    sqli: {
      name: 'SQL Injection',
      description: 'Detects SQL injection vulnerabilities',
      payloads: ["' OR '1'='1", "' UNION SELECT NULL--", "'; DROP TABLE users--"]
    },
    xss: {
      name: 'Cross-Site Scripting',
      description: 'Detects XSS vulnerabilities',
      payloads: ["<script>alert('XSS')</script>", "<img src=x onerror=alert('XSS')>"]
    },
    lfi: {
      name: 'Local File Inclusion',
      description: 'Detects LFI vulnerabilities',
      payloads: ["../../../etc/passwd", "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts"]
    },
    rce: {
      name: 'Remote Code Execution',
      description: 'Detects RCE vulnerabilities',
      payloads: ["; ls -la", "| whoami", "`id`"]
    },
    path_traversal: {
      name: 'Path Traversal',
      description: 'Detects path traversal vulnerabilities',
      payloads: ["../", "..\\", "....//"]
    },
    csrf: {
      name: 'Cross-Site Request Forgery',
      description: 'Detects CSRF vulnerabilities'
    },
    ssrf: {
      name: 'Server-Side Request Forgery',
      description: 'Detects SSRF vulnerabilities'
    },
    xxe: {
      name: 'XML External Entity',
      description: 'Detects XXE vulnerabilities'
    }
  };

  return c.json({
    success: true,
    data: scanTypes
  });
}));

// Helper function to perform vulnerability scans
async function performVulnerabilityScans(scanId: string, target: string, scanTypes: VulnType[], options: any) {
  const startTime = Date.now();
  
  try {
    // Update status to running
    await db
      .update(vulnScans)
      .set({ status: 'running' })
      .where(eq(vulnScans.id, scanId));

    // Perform scans for each type
    const vulnerabilities = [];
    
    for (const scanType of scanTypes) {
      const vulns = await performSingleScan(target, scanType, options);
      vulnerabilities.push(...vulns);
    }

    const scanDuration = Date.now() - startTime;

    // Update with results
    await db
      .update(vulnScans)
      .set({
        vulnerabilities,
        scanDuration,
        status: 'completed',
        completedAt: new Date()
      })
      .where(eq(vulnScans.id, scanId));

  } catch (error) {
    console.error('Vulnerability scan error:', error);
    
    const scanDuration = Date.now() - startTime;
    
    // Update status to failed
    await db
      .update(vulnScans)
      .set({
        status: 'failed',
        scanDuration,
        completedAt: new Date()
      })
      .where(eq(vulnScans.id, scanId));
  }
}

async function performSingleScan(target: string, scanType: VulnType, options: any) {
  // Simulate scan delay
  await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

  // Mock vulnerability detection
  const vulnerabilities = [];
  
  if (Math.random() > 0.7) { // 30% chance of finding vulnerability
    vulnerabilities.push({
      type: scanType,
      severity: ['low', 'medium', 'high', 'critical'][Math.floor(Math.random() * 4)],
      title: `${scanType.toUpperCase()} vulnerability detected`,
      description: `Potential ${scanType} vulnerability found in ${target}`,
      url: target,
      payload: getRandomPayload(scanType),
      proof: `Mock proof of ${scanType} vulnerability`,
      recommendation: `Fix the ${scanType} vulnerability by implementing proper input validation`
    });
  }

  return vulnerabilities;
}

function getRandomPayload(scanType: VulnType): string {
  const payloads = {
    [VulnType.SQLI]: ["' OR '1'='1", "' UNION SELECT NULL--", "'; DROP TABLE users--"],
    [VulnType.XSS]: ["<script>alert('XSS')</script>", "<img src=x onerror=alert('XSS')>"],
    [VulnType.LFI]: ["../../../etc/passwd", "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts"],
    [VulnType.RCE]: ["; ls -la", "| whoami", "`id`"],
    [VulnType.PATH_TRAVERSAL]: ["../", "..\\", "....//"],
    [VulnType.CSRF]: ["<form method='POST'>", "<img src='csrf-payload'>"],
    [VulnType.SSRF]: ["http://localhost:22", "http://***************/"],
    [VulnType.XXE]: ["<!ENTITY xxe SYSTEM 'file:///etc/passwd'>", "<!ENTITY xxe SYSTEM 'http://attacker.com/'>"]
  };

  const typePayloads = payloads[scanType] || ['generic-payload'];
  return typePayloads[Math.floor(Math.random() * typePayloads.length)];
}

export default vulnRoutes;
