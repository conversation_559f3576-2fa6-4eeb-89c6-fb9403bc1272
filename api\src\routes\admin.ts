import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import { db } from '../database/connection';
import { users, apiKeys, osintResults, vulnScans, fileAnalysis, payments, auditLogs } from '../database/schema';
import { eq, count, desc, gte } from 'drizzle-orm';
import { generateId } from '@kodexguard/shared';
import { asyncHandler, validationError, notFoundError, forbiddenError } from '../middleware/error-handler';
import { requireRole } from '../middleware/auth';

const adminRoutes = new Hono();

// Apply admin role requirement to all routes
adminRoutes.use('*', requireRole(['super_admin', 'admin']));

// Dashboard statistics
adminRoutes.get('/dashboard', asyncHandler(async (c) => {
  const user = c.get('user');

  // Get basic counts
  const [userCount] = await db.select({ count: count() }).from(users);
  const [osintCount] = await db.select({ count: count() }).from(osintResults);
  const [vulnCount] = await db.select({ count: count() }).from(vulnScans);
  const [fileCount] = await db.select({ count: count() }).from(fileAnalysis);
  const [paymentCount] = await db.select({ count: count() }).from(payments);

  // Get recent activity (last 30 days)
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  const [recentUsers] = await db
    .select({ count: count() })
    .from(users)
    .where(gte(users.createdAt, thirtyDaysAgo));

  const [recentScans] = await db
    .select({ count: count() })
    .from(vulnScans)
    .where(gte(vulnScans.createdAt, thirtyDaysAgo));

  // Get plan distribution
  const planDistribution = await db
    .select({ plan: users.plan, count: count() })
    .from(users)
    .groupBy(users.plan);

  const stats = {
    overview: {
      totalUsers: userCount.count,
      totalOsintQueries: osintCount.count,
      totalVulnScans: vulnCount.count,
      totalFileAnalysis: fileCount.count,
      totalPayments: paymentCount.count
    },
    recent: {
      newUsers: recentUsers.count,
      newScans: recentScans.count
    },
    planDistribution: planDistribution.reduce((acc, item) => {
      acc[item.plan] = item.count;
      return acc;
    }, {} as Record<string, number>)
  };

  return c.json({
    success: true,
    data: stats
  });
}));

// User management
adminRoutes.get('/users', asyncHandler(async (c) => {
  const page = parseInt(c.req.query('page') || '1');
  const limit = parseInt(c.req.query('limit') || '20');
  const search = c.req.query('search');
  const plan = c.req.query('plan');
  const offset = (page - 1) * limit;

  let query = db.select().from(users);

  // Apply filters
  if (search) {
    query = query.where(
      eq(users.username, search) || eq(users.email, search)
    );
  }

  if (plan) {
    query = query.where(eq(users.plan, plan));
  }

  const userList = await query
    .orderBy(desc(users.createdAt))
    .limit(limit)
    .offset(offset);

  // Remove sensitive data
  const sanitizedUsers = userList.map(user => ({
    ...user,
    password: undefined
  }));

  return c.json({
    success: true,
    data: sanitizedUsers,
    pagination: {
      page,
      limit,
      total: sanitizedUsers.length
    }
  });
}));

// Update user
const updateUserSchema = z.object({
  fullName: z.string().optional(),
  role: z.enum(['user', 'admin', 'super_admin']).optional(),
  plan: z.enum(['free', 'student', 'hobby', 'bughunter', 'cybersecurity']).optional(),
  isActive: z.boolean().optional(),
  planExpiry: z.string().datetime().optional()
});

adminRoutes.put('/users/:id', zValidator('json', updateUserSchema), asyncHandler(async (c) => {
  const userId = c.req.param('id');
  const updateData = c.req.valid('json');
  const currentUser = c.get('user');

  // Check if user exists
  const [existingUser] = await db
    .select()
    .from(users)
    .where(eq(users.id, userId))
    .limit(1);

  if (!existingUser) {
    throw notFoundError('User');
  }

  // Only super admin can change roles
  if (updateData.role && currentUser.role !== 'super_admin') {
    throw forbiddenError('Only super admin can change user roles');
  }

  // Convert planExpiry string to Date if provided
  const updatePayload = {
    ...updateData,
    planExpiry: updateData.planExpiry ? new Date(updateData.planExpiry) : undefined,
    updatedAt: new Date()
  };

  await db
    .update(users)
    .set(updatePayload)
    .where(eq(users.id, userId));

  // Log the action
  await logAdminAction(currentUser.id, 'update_user', 'user', userId, updateData);

  return c.json({
    success: true,
    message: 'User updated successfully'
  });
}));

// Delete user
adminRoutes.delete('/users/:id', requireRole(['super_admin']), asyncHandler(async (c) => {
  const userId = c.req.param('id');
  const currentUser = c.get('user');

  // Check if user exists
  const [existingUser] = await db
    .select()
    .from(users)
    .where(eq(users.id, userId))
    .limit(1);

  if (!existingUser) {
    throw notFoundError('User');
  }

  // Prevent self-deletion
  if (userId === currentUser.id) {
    throw validationError('Cannot delete your own account');
  }

  // Delete user and related data
  await db.delete(apiKeys).where(eq(apiKeys.userId, userId));
  await db.delete(osintResults).where(eq(osintResults.userId, userId));
  await db.delete(vulnScans).where(eq(vulnScans.userId, userId));
  await db.delete(fileAnalysis).where(eq(fileAnalysis.userId, userId));
  await db.delete(payments).where(eq(payments.userId, userId));
  await db.delete(users).where(eq(users.id, userId));

  // Log the action
  await logAdminAction(currentUser.id, 'delete_user', 'user', userId, { username: existingUser.username });

  return c.json({
    success: true,
    message: 'User deleted successfully'
  });
}));

// System settings
adminRoutes.get('/settings', requireRole(['super_admin']), asyncHandler(async (c) => {
  const settings = {
    maintenanceMode: process.env.MAINTENANCE_MODE === 'true',
    maintenanceMessage: process.env.MAINTENANCE_MESSAGE || 'System sedang dalam maintenance',
    registrationEnabled: true,
    apiRateLimit: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'),
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '10485760'),
    allowedFileTypes: process.env.ALLOWED_FILE_TYPES?.split(',') || ['text/plain', 'application/javascript']
  };

  return c.json({
    success: true,
    data: settings
  });
}));

// Update system settings
const updateSettingsSchema = z.object({
  maintenanceMode: z.boolean().optional(),
  maintenanceMessage: z.string().optional(),
  registrationEnabled: z.boolean().optional(),
  apiRateLimit: z.number().optional(),
  maxFileSize: z.number().optional()
});

adminRoutes.put('/settings', requireRole(['super_admin']), zValidator('json', updateSettingsSchema), asyncHandler(async (c) => {
  const settings = c.req.valid('json');
  const currentUser = c.get('user');

  // In a real implementation, these would be stored in database or config file
  // For now, we'll just return success
  
  // Log the action
  await logAdminAction(currentUser.id, 'update_settings', 'system', 'settings', settings);

  return c.json({
    success: true,
    message: 'Settings updated successfully',
    data: settings
  });
}));

// Audit logs
adminRoutes.get('/audit-logs', asyncHandler(async (c) => {
  const page = parseInt(c.req.query('page') || '1');
  const limit = parseInt(c.req.query('limit') || '50');
  const action = c.req.query('action');
  const userId = c.req.query('userId');
  const offset = (page - 1) * limit;

  let query = db.select().from(auditLogs);

  // Apply filters
  if (action) {
    query = query.where(eq(auditLogs.action, action));
  }

  if (userId) {
    query = query.where(eq(auditLogs.userId, userId));
  }

  const logs = await query
    .orderBy(desc(auditLogs.createdAt))
    .limit(limit)
    .offset(offset);

  return c.json({
    success: true,
    data: logs,
    pagination: {
      page,
      limit,
      total: logs.length
    }
  });
}));

// Payment management
adminRoutes.get('/payments', asyncHandler(async (c) => {
  const page = parseInt(c.req.query('page') || '1');
  const limit = parseInt(c.req.query('limit') || '20');
  const status = c.req.query('status');
  const gateway = c.req.query('gateway');
  const offset = (page - 1) * limit;

  let query = db.select().from(payments);

  // Apply filters
  if (status) {
    query = query.where(eq(payments.status, status));
  }

  if (gateway) {
    query = query.where(eq(payments.gateway, gateway));
  }

  const paymentList = await query
    .orderBy(desc(payments.createdAt))
    .limit(limit)
    .offset(offset);

  return c.json({
    success: true,
    data: paymentList,
    pagination: {
      page,
      limit,
      total: paymentList.length
    }
  });
}));

// Update payment status
const updatePaymentSchema = z.object({
  status: z.enum(['pending', 'completed', 'failed', 'cancelled']),
  gatewayTransactionId: z.string().optional()
});

adminRoutes.put('/payments/:id', zValidator('json', updatePaymentSchema), asyncHandler(async (c) => {
  const paymentId = c.req.param('id');
  const { status, gatewayTransactionId } = c.req.valid('json');
  const currentUser = c.get('user');

  // Check if payment exists
  const [existingPayment] = await db
    .select()
    .from(payments)
    .where(eq(payments.id, paymentId))
    .limit(1);

  if (!existingPayment) {
    throw notFoundError('Payment');
  }

  await db
    .update(payments)
    .set({
      status,
      gatewayTransactionId,
      updatedAt: new Date()
    })
    .where(eq(payments.id, paymentId));

  // If payment completed, update user plan
  if (status === 'completed' && existingPayment.status !== 'completed') {
    const planExpiry = new Date();
    const metadata = existingPayment.metadata as any;
    
    switch (metadata?.duration) {
      case 'daily':
        planExpiry.setDate(planExpiry.getDate() + 1);
        break;
      case 'weekly':
        planExpiry.setDate(planExpiry.getDate() + 7);
        break;
      case 'monthly':
        planExpiry.setMonth(planExpiry.getMonth() + 1);
        break;
      case 'yearly':
        planExpiry.setFullYear(planExpiry.getFullYear() + 1);
        break;
    }

    await db
      .update(users)
      .set({
        plan: metadata?.planType || 'hobby',
        planExpiry,
        updatedAt: new Date()
      })
      .where(eq(users.id, existingPayment.userId));
  }

  // Log the action
  await logAdminAction(currentUser.id, 'update_payment', 'payment', paymentId, { status, gatewayTransactionId });

  return c.json({
    success: true,
    message: 'Payment updated successfully'
  });
}));

// Helper function to log admin actions
async function logAdminAction(userId: string, action: string, resource: string, resourceId: string, details: any) {
  await db.insert(auditLogs).values({
    id: generateId(),
    userId,
    action,
    resource,
    resourceId,
    details,
    ipAddress: '127.0.0.1', // In real implementation, get from request
    userAgent: 'Admin Panel' // In real implementation, get from request
  });
}

export default adminRoutes;
