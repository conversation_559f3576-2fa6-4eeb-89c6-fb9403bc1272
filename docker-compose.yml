version: '3.8'

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: kodexguard-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD:-kodexguard_root}
      MYSQL_DATABASE: ${DB_NAME:-kodexguard}
      MYSQL_USER: ${DB_USER:-kodexguard}
      MYSQL_PASSWORD: ${DB_PASSWORD:-kodexguard_pass}
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./api/database/init:/docker-entrypoint-initdb.d
    networks:
      - kodexguard-network
    command: --default-authentication-plugin=mysql_native_password

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: kodexguard-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - kodexguard-network
    command: redis-server --appendonly yes

  # API Backend
  api:
    build:
      context: .
      dockerfile: api/Dockerfile
    container_name: kodexguard-api
    restart: unless-stopped
    environment:
      NODE_ENV: production
      DB_HOST: mysql
      DB_PORT: 3306
      DB_USER: ${DB_USER:-kodexguard}
      DB_PASSWORD: ${DB_PASSWORD:-kodexguard_pass}
      DB_NAME: ${DB_NAME:-kodexguard}
      REDIS_URL: redis://redis:6379
      JWT_SECRET: ${JWT_SECRET}
      CORS_ORIGIN: ${CORS_ORIGIN:-http://localhost:3000}
    ports:
      - "3001:3001"
    depends_on:
      - mysql
      - redis
    volumes:
      - ./api/uploads:/app/uploads
      - ./api/sessions:/app/sessions
      - ./api/logs:/app/logs
    networks:
      - kodexguard-network

  # Web Frontend
  web:
    build:
      context: .
      dockerfile: web/Dockerfile
    container_name: kodexguard-web
    restart: unless-stopped
    environment:
      NEXT_PUBLIC_API_URL: ${NEXT_PUBLIC_API_URL:-http://localhost:3001}
      NEXT_PUBLIC_WS_URL: ${NEXT_PUBLIC_WS_URL:-ws://localhost:3001}
    ports:
      - "3000:3000"
    depends_on:
      - api
    networks:
      - kodexguard-network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: kodexguard-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/ssl:/etc/nginx/ssl
      - ./nginx/logs:/var/log/nginx
    depends_on:
      - web
      - api
    networks:
      - kodexguard-network

  # Bot Service (Optional)
  bot:
    build:
      context: .
      dockerfile: bot/Dockerfile
    container_name: kodexguard-bot
    restart: unless-stopped
    environment:
      API_URL: http://api:3001
      TELEGRAM_BOT_TOKEN: ${TELEGRAM_BOT_TOKEN}
      WHATSAPP_SESSION_PATH: /app/sessions/whatsapp
    volumes:
      - ./bot/sessions:/app/sessions
      - ./bot/logs:/app/logs
    depends_on:
      - api
    networks:
      - kodexguard-network
    profiles:
      - bot

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  kodexguard-network:
    driver: bridge
