import { mysqlTable, varchar, text, int, boolean, timestamp, decimal, json, index, uniqueIndex } from 'drizzle-orm/mysql-core';
import { relations } from 'drizzle-orm';

// Users Table
export const users = mysqlTable('users', {
  id: varchar('id', { length: 36 }).primaryKey(),
  username: varchar('username', { length: 30 }).notNull().unique(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  password: varchar('password', { length: 255 }).notNull(),
  fullName: varchar('full_name', { length: 100 }).notNull(),
  avatar: text('avatar'),
  bio: text('bio'),
  role: varchar('role', { length: 20 }).notNull().default('user'),
  plan: varchar('plan', { length: 20 }).notNull().default('free'),
  planExpiry: timestamp('plan_expiry'),
  isActive: boolean('is_active').notNull().default(true),
  emailVerified: boolean('email_verified').notNull().default(false),
  lastLogin: timestamp('last_login'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow().onUpdateNow()
}, (table) => ({
  usernameIdx: index('username_idx').on(table.username),
  emailIdx: index('email_idx').on(table.email),
  planIdx: index('plan_idx').on(table.plan)
}));

// API Keys Table
export const apiKeys = mysqlTable('api_keys', {
  id: varchar('id', { length: 36 }).primaryKey(),
  userId: varchar('user_id', { length: 36 }).notNull(),
  name: varchar('name', { length: 100 }).notNull(),
  key: varchar('key', { length: 64 }).notNull().unique(),
  isActive: boolean('is_active').notNull().default(true),
  lastUsed: timestamp('last_used'),
  usageCount: int('usage_count').notNull().default(0),
  rateLimit: int('rate_limit').notNull().default(100),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  expiresAt: timestamp('expires_at')
}, (table) => ({
  userIdIdx: index('user_id_idx').on(table.userId),
  keyIdx: uniqueIndex('key_idx').on(table.key)
}));

// OSINT Results Table
export const osintResults = mysqlTable('osint_results', {
  id: varchar('id', { length: 36 }).primaryKey(),
  userId: varchar('user_id', { length: 36 }).notNull(),
  type: varchar('type', { length: 20 }).notNull(),
  query: varchar('query', { length: 255 }).notNull(),
  results: json('results'),
  confidence: decimal('confidence', { precision: 3, scale: 2 }),
  sources: json('sources'),
  status: varchar('status', { length: 20 }).notNull().default('pending'),
  createdAt: timestamp('created_at').notNull().defaultNow()
}, (table) => ({
  userIdIdx: index('user_id_idx').on(table.userId),
  typeIdx: index('type_idx').on(table.type),
  statusIdx: index('status_idx').on(table.status)
}));

// Vulnerability Scans Table
export const vulnScans = mysqlTable('vuln_scans', {
  id: varchar('id', { length: 36 }).primaryKey(),
  userId: varchar('user_id', { length: 36 }).notNull(),
  target: varchar('target', { length: 255 }).notNull(),
  scanTypes: json('scan_types'),
  vulnerabilities: json('vulnerabilities'),
  scanDuration: int('scan_duration'),
  status: varchar('status', { length: 20 }).notNull().default('pending'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  completedAt: timestamp('completed_at')
}, (table) => ({
  userIdIdx: index('user_id_idx').on(table.userId),
  targetIdx: index('target_idx').on(table.target),
  statusIdx: index('status_idx').on(table.status)
}));

// File Analysis Table
export const fileAnalysis = mysqlTable('file_analysis', {
  id: varchar('id', { length: 36 }).primaryKey(),
  userId: varchar('user_id', { length: 36 }).notNull(),
  fileName: varchar('file_name', { length: 255 }).notNull(),
  fileType: varchar('file_type', { length: 100 }).notNull(),
  fileSize: int('file_size').notNull(),
  filePath: varchar('file_path', { length: 500 }),
  threats: json('threats'),
  secrets: json('secrets'),
  hash: json('hash'),
  status: varchar('status', { length: 20 }).notNull().default('pending'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  completedAt: timestamp('completed_at')
}, (table) => ({
  userIdIdx: index('user_id_idx').on(table.userId),
  fileTypeIdx: index('file_type_idx').on(table.fileType),
  statusIdx: index('status_idx').on(table.status)
}));

// Bot Configurations Table
export const botConfigs = mysqlTable('bot_configs', {
  id: varchar('id', { length: 36 }).primaryKey(),
  type: varchar('type', { length: 20 }).notNull(),
  name: varchar('name', { length: 100 }).notNull(),
  token: text('token'),
  phoneNumber: varchar('phone_number', { length: 20 }),
  isActive: boolean('is_active').notNull().default(false),
  qrCode: text('qr_code'),
  lastSeen: timestamp('last_seen'),
  config: json('config'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow().onUpdateNow()
}, (table) => ({
  typeIdx: index('type_idx').on(table.type)
}));

// Plans Table
export const plans = mysqlTable('plans', {
  id: varchar('id', { length: 36 }).primaryKey(),
  name: varchar('name', { length: 50 }).notNull(),
  type: varchar('type', { length: 20 }).notNull(),
  price: decimal('price', { precision: 10, scale: 2 }).notNull(),
  duration: varchar('duration', { length: 20 }).notNull(),
  features: json('features'),
  limits: json('limits'),
  isActive: boolean('is_active').notNull().default(true),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow().onUpdateNow()
}, (table) => ({
  typeIdx: index('type_idx').on(table.type)
}));

// Payments Table
export const payments = mysqlTable('payments', {
  id: varchar('id', { length: 36 }).primaryKey(),
  userId: varchar('user_id', { length: 36 }).notNull(),
  planId: varchar('plan_id', { length: 36 }).notNull(),
  amount: decimal('amount', { precision: 10, scale: 2 }).notNull(),
  currency: varchar('currency', { length: 3 }).notNull().default('IDR'),
  gateway: varchar('gateway', { length: 20 }).notNull(),
  gatewayTransactionId: varchar('gateway_transaction_id', { length: 100 }),
  status: varchar('status', { length: 20 }).notNull().default('pending'),
  metadata: json('metadata'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow().onUpdateNow()
}, (table) => ({
  userIdIdx: index('user_id_idx').on(table.userId),
  statusIdx: index('status_idx').on(table.status),
  gatewayIdx: index('gateway_idx').on(table.gateway)
}));

// CVE Database Table
export const cveDatabase = mysqlTable('cve_database', {
  id: varchar('id', { length: 36 }).primaryKey(),
  cveId: varchar('cve_id', { length: 20 }).notNull().unique(),
  description: text('description').notNull(),
  severity: varchar('severity', { length: 20 }).notNull(),
  cvssScore: decimal('cvss_score', { precision: 3, scale: 1 }),
  publishedDate: timestamp('published_date'),
  lastModified: timestamp('last_modified'),
  references: json('references'),
  affectedProducts: json('affected_products'),
  exploitAvailable: boolean('exploit_available').notNull().default(false),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow().onUpdateNow()
}, (table) => ({
  cveIdIdx: uniqueIndex('cve_id_idx').on(table.cveId),
  severityIdx: index('severity_idx').on(table.severity),
  publishedDateIdx: index('published_date_idx').on(table.publishedDate)
}));

// Dork Presets Table
export const dorkPresets = mysqlTable('dork_presets', {
  id: varchar('id', { length: 36 }).primaryKey(),
  name: varchar('name', { length: 100 }).notNull(),
  query: text('query').notNull(),
  category: varchar('category', { length: 50 }).notNull(),
  description: text('description'),
  isPublic: boolean('is_public').notNull().default(true),
  createdBy: varchar('created_by', { length: 36 }),
  usageCount: int('usage_count').notNull().default(0),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow().onUpdateNow()
}, (table) => ({
  categoryIdx: index('category_idx').on(table.category),
  createdByIdx: index('created_by_idx').on(table.createdBy)
}));

// Audit Logs Table
export const auditLogs = mysqlTable('audit_logs', {
  id: varchar('id', { length: 36 }).primaryKey(),
  userId: varchar('user_id', { length: 36 }),
  action: varchar('action', { length: 100 }).notNull(),
  resource: varchar('resource', { length: 100 }).notNull(),
  resourceId: varchar('resource_id', { length: 36 }),
  details: json('details'),
  ipAddress: varchar('ip_address', { length: 45 }),
  userAgent: text('user_agent'),
  createdAt: timestamp('created_at').notNull().defaultNow()
}, (table) => ({
  userIdIdx: index('user_id_idx').on(table.userId),
  actionIdx: index('action_idx').on(table.action),
  resourceIdx: index('resource_idx').on(table.resource),
  createdAtIdx: index('created_at_idx').on(table.createdAt)
}));

// Leaderboard Table
export const leaderboard = mysqlTable('leaderboard', {
  id: varchar('id', { length: 36 }).primaryKey(),
  userId: varchar('user_id', { length: 36 }).notNull(),
  totalScans: int('total_scans').notNull().default(0),
  totalVulns: int('total_vulns').notNull().default(0),
  totalReports: int('total_reports').notNull().default(0),
  score: int('score').notNull().default(0),
  rank: int('rank').notNull().default(0),
  lastUpdated: timestamp('last_updated').notNull().defaultNow().onUpdateNow()
}, (table) => ({
  userIdIdx: uniqueIndex('user_id_idx').on(table.userId),
  scoreIdx: index('score_idx').on(table.score),
  rankIdx: index('rank_idx').on(table.rank)
}));

// Relations
export const usersRelations = relations(users, ({ many, one }) => ({
  apiKeys: many(apiKeys),
  osintResults: many(osintResults),
  vulnScans: many(vulnScans),
  fileAnalysis: many(fileAnalysis),
  payments: many(payments),
  leaderboard: one(leaderboard)
}));

export const apiKeysRelations = relations(apiKeys, ({ one }) => ({
  user: one(users, {
    fields: [apiKeys.userId],
    references: [users.id]
  })
}));

export const osintResultsRelations = relations(osintResults, ({ one }) => ({
  user: one(users, {
    fields: [osintResults.userId],
    references: [users.id]
  })
}));

export const vulnScansRelations = relations(vulnScans, ({ one }) => ({
  user: one(users, {
    fields: [vulnScans.userId],
    references: [users.id]
  })
}));

export const fileAnalysisRelations = relations(fileAnalysis, ({ one }) => ({
  user: one(users, {
    fields: [fileAnalysis.userId],
    references: [users.id]
  })
}));

export const paymentsRelations = relations(payments, ({ one }) => ({
  user: one(users, {
    fields: [payments.userId],
    references: [users.id]
  }),
  plan: one(plans, {
    fields: [payments.planId],
    references: [plans.id]
  })
}));

export const leaderboardRelations = relations(leaderboard, ({ one }) => ({
  user: one(users, {
    fields: [leaderboard.userId],
    references: [users.id]
  })
}));
