# Dependencies
node_modules/
*/node_modules/
.pnp
.pnp.js

# Production builds
dist/
build/
out/
.next/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.production
api/.env
web/.env.local
mobile/.env

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Database
*.sqlite
*.sqlite3
*.db

# Uploads and user content
uploads/
sessions/
backups/

# SSL certificates
ssl/
*.pem
*.key
*.crt
*.csr

# Docker
.dockerignore

# Expo
.expo/
dist/
web-build/

# Bun
.bun

# Local development
.local/

# Testing
coverage/
.nyc_output/

# Storybook
storybook-static/

# Temporary files
*.tmp
*.temp

# Lock files (keep bun.lockb, ignore others)
package-lock.json
yarn.lock

# IDE files
*.sublime-project
*.sublime-workspace

# JetBrains IDEs
.idea/
*.iml
*.ipr
*.iws

# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Mobile specific
# React Native
.expo/
.expo-shared/

# Android
android/app/build/
android/build/
android/.gradle/
android/local.properties
android/app/release/

# iOS
ios/build/
ios/Pods/
ios/*.xcworkspace/xcuserdata/
ios/*.xcodeproj/xcuserdata/

# Fastlane
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots/
fastlane/test_output/

# Bundle artifacts
*.jsbundle

# CocoaPods
ios/Pods/

# Flipper
ios/Flipper/

# API specific
api/uploads/
api/sessions/
api/logs/

# Web specific
web/.next/
web/out/

# Mobile specific
mobile/.expo/
mobile/dist/
mobile/web-build/

# Nginx logs
nginx/logs/

# Database dumps
*.sql
*.dump

# Monitoring and analytics
.sentry/

# Terraform (if used for infrastructure)
*.tfstate
*.tfstate.*
.terraform/

# Kubernetes (if used for deployment)
*.kubeconfig

# Helm (if used for Kubernetes deployment)
charts/*/charts/
charts/*/requirements.lock

# Local configuration overrides
docker-compose.override.yml
docker-compose.local.yml

# Performance monitoring
.clinic/

# Profiling
*.prof

# Benchmarking
benchmark-results/

# Documentation builds
docs/_build/
docs/.doctrees/

# Translation files
*.pot

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/
