{"name": "@kodexguard/api", "version": "1.0.0", "description": "KodeXGuard Backend API with Bun.js", "main": "src/index.ts", "scripts": {"dev": "bun run --watch src/index.ts", "build": "bun build src/index.ts --outdir dist --target bun", "start": "bun run dist/index.js", "test": "bun test", "test:watch": "bun test --watch", "db:migrate": "bun run src/database/migrate.ts", "db:seed": "bun run src/database/seed.ts", "db:reset": "bun run src/database/reset.ts", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "type-check": "tsc --noEmit"}, "dependencies": {"@kodexguard/shared": "workspace:*", "hono": "^3.12.0", "mysql2": "^3.6.5", "drizzle-orm": "^0.29.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "zod": "^3.22.4", "cors": "^2.8.5", "helmet": "^7.1.0", "rate-limiter-flexible": "^4.0.1", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "axios": "^1.6.2", "cheerio": "^1.0.0-rc.12", "puppeteer": "^21.6.1", "venom-bot": "^5.0.17", "node-telegram-bot-api": "^0.64.0", "winston": "^3.11.0", "dotenv": "^16.3.1"}, "devDependencies": {"@types/node": "^20.10.0", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/multer": "^1.4.11", "@types/node-cron": "^3.0.11", "@types/node-telegram-bot-api": "^0.64.7", "typescript": "^5.3.0", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "drizzle-kit": "^0.20.6"}, "type": "module"}