#!/bin/bash

# KodeXGuard Setup Script
# This script automates the setup process for KodeXGuard platform

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to generate random string
generate_random_string() {
    openssl rand -base64 32 | tr -d "=+/" | cut -c1-32
}

# Check system requirements
check_requirements() {
    print_status "Checking system requirements..."
    
    # Check Node.js
    if ! command_exists node; then
        print_error "Node.js is not installed. Please install Node.js 18+ first."
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        print_error "Node.js version 18+ is required. Current version: $(node --version)"
        exit 1
    fi
    
    # Check Bun.js
    if ! command_exists bun; then
        print_warning "Bun.js is not installed. Installing Bun.js..."
        curl -fsSL https://bun.sh/install | bash
        export PATH="$HOME/.bun/bin:$PATH"
        
        if ! command_exists bun; then
            print_error "Failed to install Bun.js. Please install manually."
            exit 1
        fi
    fi
    
    # Check MySQL
    if ! command_exists mysql; then
        print_warning "MySQL is not installed. Please install MySQL 8.0+ manually."
        print_status "On Ubuntu/Debian: sudo apt install mysql-server"
        print_status "On macOS: brew install mysql"
        print_status "On Windows: Download from https://dev.mysql.com/downloads/mysql/"
    fi
    
    # Check Git
    if ! command_exists git; then
        print_error "Git is not installed. Please install Git first."
        exit 1
    fi
    
    print_success "System requirements check completed!"
}

# Install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    
    # Install root dependencies
    bun install
    
    # Install workspace dependencies
    bun install --workspaces
    
    print_success "Dependencies installed successfully!"
}

# Setup environment files
setup_environment() {
    print_status "Setting up environment files..."
    
    # API environment
    if [ ! -f "api/.env" ]; then
        cp api/.env.example api/.env
        
        # Generate JWT secret
        JWT_SECRET=$(generate_random_string)
        sed -i.bak "s/your_super_secret_jwt_key_here/$JWT_SECRET/g" api/.env
        rm api/.env.bak
        
        print_success "API environment file created with generated JWT secret"
    else
        print_warning "API environment file already exists"
    fi
    
    # Web environment
    if [ ! -f "web/.env.local" ]; then
        cp web/.env.example web/.env.local
        print_success "Web environment file created"
    else
        print_warning "Web environment file already exists"
    fi
}

# Setup database
setup_database() {
    print_status "Setting up database..."
    
    # Check if MySQL is running
    if ! pgrep -x "mysqld" > /dev/null; then
        print_warning "MySQL is not running. Please start MySQL service first."
        print_status "On Ubuntu/Debian: sudo systemctl start mysql"
        print_status "On macOS: brew services start mysql"
        return 1
    fi
    
    # Prompt for database credentials
    echo -n "Enter MySQL root password: "
    read -s MYSQL_ROOT_PASSWORD
    echo
    
    # Create database and user
    mysql -u root -p"$MYSQL_ROOT_PASSWORD" <<EOF
CREATE DATABASE IF NOT EXISTS kodexguard;
CREATE USER IF NOT EXISTS 'kodexguard'@'localhost' IDENTIFIED BY 'kodexguard_pass';
GRANT ALL PRIVILEGES ON kodexguard.* TO 'kodexguard'@'localhost';
FLUSH PRIVILEGES;
EOF
    
    if [ $? -eq 0 ]; then
        print_success "Database created successfully!"
        
        # Update environment file with database credentials
        sed -i.bak "s/DB_PASSWORD=your_password/DB_PASSWORD=kodexguard_pass/g" api/.env
        rm api/.env.bak
        
        # Run migrations
        cd api
        bun run db:generate
        bun run db:migrate
        bun run db:seed
        cd ..
        
        print_success "Database migrations completed!"
    else
        print_error "Failed to create database. Please check your MySQL credentials."
        return 1
    fi
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    mkdir -p api/uploads
    mkdir -p api/sessions
    mkdir -p api/logs
    mkdir -p web/.next
    mkdir -p mobile/assets/fonts
    
    print_success "Directories created successfully!"
}

# Setup mobile app (optional)
setup_mobile() {
    read -p "Do you want to setup mobile app? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Setting up mobile app..."
        
        # Check if Expo CLI is installed
        if ! command_exists expo; then
            print_status "Installing Expo CLI..."
            npm install -g @expo/cli
        fi
        
        # Download font files (placeholder)
        print_status "Setting up mobile fonts..."
        # In real implementation, download actual font files
        touch mobile/assets/fonts/Inter-Regular.ttf
        touch mobile/assets/fonts/Inter-Medium.ttf
        touch mobile/assets/fonts/Inter-SemiBold.ttf
        touch mobile/assets/fonts/Inter-Bold.ttf
        touch mobile/assets/fonts/JetBrainsMono-Regular.ttf
        touch mobile/assets/fonts/JetBrainsMono-Bold.ttf
        
        print_success "Mobile app setup completed!"
    fi
}

# Start development servers
start_servers() {
    read -p "Do you want to start development servers now? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Starting development servers..."
        
        # Start API server in background
        cd api
        bun run dev &
        API_PID=$!
        cd ..
        
        # Wait a moment for API to start
        sleep 3
        
        # Start web server in background
        cd web
        bun run dev &
        WEB_PID=$!
        cd ..
        
        print_success "Development servers started!"
        print_status "API Server: http://localhost:3001"
        print_status "Web App: http://localhost:3000"
        print_status "API Docs: http://localhost:3001/docs"
        
        echo
        print_status "Press Ctrl+C to stop all servers"
        
        # Wait for user interrupt
        trap "kill $API_PID $WEB_PID; exit" INT
        wait
    fi
}

# Main setup function
main() {
    echo -e "${BLUE}"
    echo "🛡️  KodeXGuard Setup Script"
    echo "=================================="
    echo -e "${NC}"
    
    check_requirements
    install_dependencies
    setup_environment
    create_directories
    setup_database
    setup_mobile
    
    echo
    print_success "🎉 KodeXGuard setup completed successfully!"
    echo
    print_status "Next steps:"
    print_status "1. Review and update environment files (api/.env, web/.env.local)"
    print_status "2. Configure payment gateways (optional)"
    print_status "3. Setup bot tokens (optional)"
    print_status "4. Start development servers: ./scripts/dev.sh"
    echo
    
    start_servers
}

# Run main function
main "$@"
