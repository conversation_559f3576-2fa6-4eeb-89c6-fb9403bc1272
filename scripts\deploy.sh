#!/bin/bash

# KodeXGuard Production Deployment Script
# This script handles production deployment with Docker

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Configuration
DOCKER_COMPOSE_FILE="docker-compose.yml"
DOCKER_COMPOSE_PROD_FILE="docker-compose.prod.yml"
BACKUP_DIR="./backups"
LOG_FILE="./logs/deploy.log"

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

# Function to check prerequisites
check_prerequisites() {
    print_info "Checking deployment prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check if running as root or with docker group
    if ! docker ps &> /dev/null; then
        print_error "Cannot access Docker. Please run as root or add user to docker group."
        exit 1
    fi
    
    # Check environment files
    if [ ! -f ".env.production" ]; then
        print_error "Production environment file (.env.production) not found."
        print_info "Please create .env.production with production settings."
        exit 1
    fi
    
    print_success "Prerequisites check passed!"
}

# Function to create backup
create_backup() {
    print_info "Creating backup..."
    
    # Create backup directory
    mkdir -p "$BACKUP_DIR"
    
    # Backup timestamp
    TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
    BACKUP_FILE="$BACKUP_DIR/kodexguard_backup_$TIMESTAMP.sql"
    
    # Backup database if running
    if docker-compose ps mysql | grep -q "Up"; then
        print_info "Backing up database..."
        docker-compose exec -T mysql mysqldump -u root -p"${DB_ROOT_PASSWORD}" kodexguard > "$BACKUP_FILE"
        
        if [ $? -eq 0 ]; then
            print_success "Database backup created: $BACKUP_FILE"
        else
            print_error "Database backup failed!"
            exit 1
        fi
    else
        print_warning "MySQL container not running, skipping database backup"
    fi
    
    # Backup uploaded files
    if [ -d "api/uploads" ]; then
        tar -czf "$BACKUP_DIR/uploads_backup_$TIMESTAMP.tar.gz" api/uploads/
        print_success "Files backup created: $BACKUP_DIR/uploads_backup_$TIMESTAMP.tar.gz"
    fi
}

# Function to build images
build_images() {
    print_info "Building Docker images..."
    
    # Build with production compose file if exists
    if [ -f "$DOCKER_COMPOSE_PROD_FILE" ]; then
        docker-compose -f "$DOCKER_COMPOSE_PROD_FILE" build --no-cache
    else
        docker-compose build --no-cache
    fi
    
    if [ $? -eq 0 ]; then
        print_success "Docker images built successfully!"
    else
        print_error "Failed to build Docker images!"
        exit 1
    fi
}

# Function to run database migrations
run_migrations() {
    print_info "Running database migrations..."
    
    # Wait for database to be ready
    print_info "Waiting for database to be ready..."
    sleep 10
    
    # Run migrations
    docker-compose exec api bun run db:migrate
    
    if [ $? -eq 0 ]; then
        print_success "Database migrations completed!"
    else
        print_error "Database migrations failed!"
        exit 1
    fi
}

# Function to deploy services
deploy_services() {
    print_info "Deploying services..."
    
    # Load environment variables
    export $(cat .env.production | xargs)
    
    # Deploy with production compose file if exists
    if [ -f "$DOCKER_COMPOSE_PROD_FILE" ]; then
        docker-compose -f "$DOCKER_COMPOSE_PROD_FILE" up -d
    else
        docker-compose up -d
    fi
    
    if [ $? -eq 0 ]; then
        print_success "Services deployed successfully!"
    else
        print_error "Failed to deploy services!"
        exit 1
    fi
}

# Function to health check
health_check() {
    print_info "Performing health checks..."
    
    # Wait for services to start
    sleep 30
    
    # Check API health
    for i in {1..30}; do
        if curl -s http://localhost:3001/health >/dev/null 2>&1; then
            print_success "API health check passed!"
            break
        fi
        sleep 2
    done
    
    if [ $i -eq 30 ]; then
        print_error "API health check failed!"
        return 1
    fi
    
    # Check web app
    for i in {1..30}; do
        if curl -s http://localhost:3000 >/dev/null 2>&1; then
            print_success "Web app health check passed!"
            break
        fi
        sleep 2
    done
    
    if [ $i -eq 30 ]; then
        print_error "Web app health check failed!"
        return 1
    fi
    
    print_success "All health checks passed!"
}

# Function to cleanup old images
cleanup_images() {
    print_info "Cleaning up old Docker images..."
    
    # Remove dangling images
    docker image prune -f
    
    # Remove old images (keep last 3 versions)
    docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.ID}}\t{{.CreatedAt}}" | \
    grep kodexguard | \
    tail -n +4 | \
    awk '{print $3}' | \
    xargs -r docker rmi
    
    print_success "Docker cleanup completed!"
}

# Function to show deployment status
show_status() {
    echo
    echo -e "${PURPLE}🚀 KodeXGuard Deployment Status${NC}"
    echo -e "${PURPLE}================================${NC}"
    echo
    
    # Show running containers
    docker-compose ps
    
    echo
    echo -e "${GREEN}📊 Service URLs:${NC}"
    echo -e "   Web App:      http://localhost:3000"
    echo -e "   API:          http://localhost:3001"
    echo -e "   API Docs:     http://localhost:3001/docs"
    echo -e "   Health Check: http://localhost:3001/health"
    echo
    
    # Show resource usage
    echo -e "${BLUE}💾 Resource Usage:${NC}"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"
    echo
}

# Function to rollback deployment
rollback() {
    print_warning "Rolling back deployment..."
    
    # Stop current services
    docker-compose down
    
    # Restore from latest backup
    LATEST_BACKUP=$(ls -t "$BACKUP_DIR"/*.sql 2>/dev/null | head -n1)
    
    if [ ! -z "$LATEST_BACKUP" ]; then
        print_info "Restoring database from: $LATEST_BACKUP"
        
        # Start only database
        docker-compose up -d mysql
        sleep 10
        
        # Restore database
        docker-compose exec -T mysql mysql -u root -p"${DB_ROOT_PASSWORD}" kodexguard < "$LATEST_BACKUP"
        
        print_success "Database restored from backup!"
    else
        print_warning "No backup found for rollback!"
    fi
    
    # Start previous version (this would need version management)
    print_info "Starting previous version..."
    docker-compose up -d
    
    print_success "Rollback completed!"
}

# Function to show help
show_help() {
    echo "KodeXGuard Production Deployment Script"
    echo
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo
    echo "Commands:"
    echo "  deploy     Full deployment (default)"
    echo "  build      Build Docker images only"
    echo "  start      Start services"
    echo "  stop       Stop services"
    echo "  restart    Restart services"
    echo "  status     Show deployment status"
    echo "  logs       Show service logs"
    echo "  backup     Create backup only"
    echo "  rollback   Rollback to previous version"
    echo "  cleanup    Cleanup old Docker images"
    echo
    echo "Options:"
    echo "  --no-backup    Skip backup creation"
    echo "  --no-build     Skip image building"
    echo "  --help         Show this help message"
    echo
    echo "Examples:"
    echo "  $0 deploy              Full deployment with backup"
    echo "  $0 deploy --no-backup  Deploy without backup"
    echo "  $0 build               Build images only"
    echo "  $0 status              Show current status"
}

# Main function
main() {
    # Create log directory
    mkdir -p logs
    
    # Log deployment start
    echo "$(date): Deployment started" >> "$LOG_FILE"
    
    case "$1" in
        build)
            check_prerequisites
            build_images
            ;;
        start)
            deploy_services
            health_check
            show_status
            ;;
        stop)
            print_info "Stopping services..."
            docker-compose down
            print_success "Services stopped!"
            ;;
        restart)
            print_info "Restarting services..."
            docker-compose restart
            health_check
            show_status
            ;;
        status)
            show_status
            ;;
        logs)
            docker-compose logs -f
            ;;
        backup)
            create_backup
            ;;
        rollback)
            rollback
            ;;
        cleanup)
            cleanup_images
            ;;
        deploy|"")
            check_prerequisites
            
            # Create backup unless --no-backup flag
            if [[ "$*" != *"--no-backup"* ]]; then
                create_backup
            fi
            
            # Build images unless --no-build flag
            if [[ "$*" != *"--no-build"* ]]; then
                build_images
            fi
            
            deploy_services
            run_migrations
            health_check
            cleanup_images
            show_status
            
            print_success "🎉 Deployment completed successfully!"
            ;;
        --help|-h)
            show_help
            ;;
        *)
            print_error "Unknown command: $1"
            show_help
            exit 1
            ;;
    esac
    
    # Log deployment end
    echo "$(date): Deployment finished" >> "$LOG_FILE"
}

# Run main function with all arguments
main "$@"
