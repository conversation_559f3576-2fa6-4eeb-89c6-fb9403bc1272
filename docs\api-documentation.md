# 📚 API Documentation - KodeXGuard

Dokumentasi lengkap API KodeXGuard untuk developer dan integrasi sistem.

## 🔗 Base URL

- **Development**: `http://localhost:3001/api/v1`
- **Production**: `https://api.kodexguard.com/api/v1`

## 🔐 Authentication

### JWT Token Authentication
```http
Authorization: Bearer <your_jwt_token>
```

### API Key Authentication
```http
X-API-Key: <your_api_key>
```

## 📋 Response Format

### Success Response
```json
{
  "success": true,
  "message": "Operation successful",
  "data": {
    // Response data
  },
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100
  }
}
```

### Error Response
```json
{
  "success": false,
  "error": "Error message",
  "code": "ERROR_CODE",
  "details": {
    // Additional error details
  }
}
```

## 🔑 Authentication Endpoints

### POST /auth/login
Login user dengan username/email dan password.

**Request Body:**
```json
{
  "identifier": "username_or_email",
  "password": "user_password"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "id": "user_id",
      "username": "username",
      "email": "<EMAIL>",
      "fullName": "Full Name",
      "role": "user",
      "plan": "free"
    },
    "token": "jwt_token_here"
  }
}
```

### POST /auth/register
Registrasi user baru.

**Request Body:**
```json
{
  "username": "new_username",
  "email": "<EMAIL>",
  "password": "secure_password",
  "fullName": "Full Name"
}
```

### GET /auth/verify
Verifikasi JWT token yang valid.

**Headers:**
```http
Authorization: Bearer <jwt_token>
```

## 👤 User Management

### GET /users/profile
Mendapatkan profil user saat ini.

### PUT /users/profile
Update profil user.

**Request Body:**
```json
{
  "fullName": "Updated Name",
  "bio": "User bio",
  "avatar": "https://avatar-url.com/image.jpg"
}
```

### GET /users/api-keys
Mendapatkan daftar API keys user.

### POST /users/api-keys
Membuat API key baru.

**Request Body:**
```json
{
  "name": "API Key Name",
  "rateLimit": 100,
  "expiresAt": "2024-12-31T23:59:59Z"
}
```

## 🔍 OSINT Endpoints

### POST /osint/search
Memulai pencarian OSINT.

**Request Body:**
```json
{
  "type": "name|nik|npwp|phone|imei|email|domain",
  "query": "search_query",
  "options": {
    "deepSearch": true,
    "includeLeaks": true,
    "includeGithub": true,
    "includeSocial": true
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "OSINT search started",
  "data": {
    "searchId": "search_id",
    "type": "name",
    "query": "masked_query",
    "status": "pending"
  }
}
```

### GET /osint/search/{id}
Mendapatkan hasil pencarian OSINT.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "search_id",
    "type": "name",
    "query": "masked_query",
    "results": [
      {
        "source": "database_name",
        "data": {
          "found": true,
          "details": "search_results"
        },
        "confidence": 85.5,
        "verified": true
      }
    ],
    "confidence": 85.5,
    "sources": ["database1", "database2"],
    "status": "completed"
  }
}
```

### GET /osint/history
Mendapatkan riwayat pencarian OSINT.

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20)

## 🛡️ Vulnerability Scanner

### POST /vulnerability/scan
Memulai vulnerability scan.

**Request Body:**
```json
{
  "target": "https://target-website.com",
  "scanTypes": ["sqli", "xss", "lfi", "rce"],
  "options": {
    "aggressive": false,
    "timeout": 60,
    "userAgent": "KodeXGuard Scanner",
    "headers": {
      "Custom-Header": "value"
    }
  }
}
```

### GET /vulnerability/scan/{id}
Mendapatkan hasil vulnerability scan.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "scan_id",
    "target": "https://target-website.com",
    "scanTypes": ["sqli", "xss"],
    "vulnerabilities": [
      {
        "type": "sqli",
        "severity": "high",
        "title": "SQL Injection vulnerability",
        "description": "SQL injection found in parameter 'id'",
        "url": "https://target-website.com/page?id=1",
        "payload": "' OR '1'='1",
        "proof": "Database error revealed",
        "recommendation": "Use parameterized queries"
      }
    ],
    "scanDuration": 30000,
    "status": "completed"
  }
}
```

### GET /vulnerability/scan-types
Mendapatkan daftar jenis scan yang tersedia.

## 📂 File Analysis

### POST /file-analysis/analyze
Menganalisis file untuk deteksi malware dan secret.

**Request Body:**
```json
{
  "fileName": "suspicious_file.php",
  "fileContent": "base64_encoded_content",
  "fileType": "application/php"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "analysisId": "analysis_id",
    "fileName": "suspicious_file.php",
    "fileType": "application/php",
    "fileSize": 1024,
    "hashes": {
      "md5": "hash_value",
      "sha1": "hash_value",
      "sha256": "hash_value",
      "sha512": "hash_value"
    },
    "status": "pending"
  }
}
```

### GET /file-analysis/analyze/{id}
Mendapatkan hasil analisis file.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "analysis_id",
    "fileName": "suspicious_file.php",
    "threats": [
      {
        "type": "webshell",
        "severity": "high",
        "description": "Potential webshell detected",
        "location": "Line 15",
        "confidence": 90
      }
    ],
    "secrets": [
      {
        "type": "api_key",
        "value": "sk-1234...****",
        "location": "Line 8",
        "confidence": 95
      }
    ],
    "status": "completed"
  }
}
```

## 🔧 Security Tools

### POST /tools/hash
Generate hash dari text.

**Request Body:**
```json
{
  "data": "text_to_hash",
  "algorithm": "sha256"
}
```

### POST /tools/encode
Encode text dengan berbagai format.

**Request Body:**
```json
{
  "data": "text_to_encode",
  "encoding": "base64|hex|rot13"
}
```

### POST /tools/decode
Decode text dari berbagai format.

**Request Body:**
```json
{
  "data": "encoded_text",
  "encoding": "base64|hex|rot13"
}
```

### POST /tools/payload
Generate payload untuk testing.

**Request Body:**
```json
{
  "type": "sqli|xss|lfi|rce|command_injection",
  "target": "optional_target_info",
  "custom": false
}
```

## 🐛 CVE Database

### GET /cve
Mendapatkan daftar CVE dengan filter.

**Query Parameters:**
- `page`: Page number
- `limit`: Items per page
- `severity`: critical|high|medium|low
- `year`: Filter by year
- `search`: Search term
- `cvssMin`: Minimum CVSS score
- `cvssMax`: Maximum CVSS score
- `hasExploit`: true|false

### GET /cve/{cveId}
Mendapatkan detail CVE spesifik.

### GET /cve/recent/list
Mendapatkan CVE terbaru.

### POST /cve/search
Pencarian CVE berdasarkan keyword.

## 🎯 Google Dorking

### GET /dorking/presets
Mendapatkan daftar dork preset.

### GET /dorking/categories
Mendapatkan kategori dork.

### POST /dorking/search
Eksekusi Google dork search.

**Request Body:**
```json
{
  "query": "site:example.com filetype:pdf",
  "limit": 10
}
```

## 🏆 Leaderboard

### GET /leaderboard
Mendapatkan leaderboard pengguna.

### GET /leaderboard/top
Mendapatkan top performers.

### GET /leaderboard/my-position
Mendapatkan posisi user di leaderboard.

## 💳 Plans & Payments

### GET /plans
Mendapatkan daftar plan yang tersedia.

### POST /plans/purchase
Membeli plan baru.

**Request Body:**
```json
{
  "planId": "plan_id",
  "gateway": "tripay|midtrans|xendit|manual",
  "duration": "monthly|yearly"
}
```

### GET /plans/current
Mendapatkan plan user saat ini.

## 🤖 Bot Management

### GET /bots/status
Mendapatkan status bot (public).

### GET /bots/commands
Mendapatkan daftar command bot.

## 👑 Admin Endpoints

### GET /admin/dashboard
Dashboard statistik admin.

### GET /admin/users
Manajemen user (admin only).

### PUT /admin/users/{id}
Update user (admin only).

### GET /admin/payments
Manajemen pembayaran (admin only).

## 📊 Rate Limiting

Rate limiting berdasarkan plan user:

| Plan | OSINT | Vuln Scan | File Analysis | API Calls |
|------|-------|-----------|---------------|-----------|
| Free | 10/day | 5/day | 3/day | 100/hour |
| Student | 50/day | 25/day | 15/day | 500/hour |
| Hobby | 100/day | 50/day | 30/day | 1000/hour |
| Bughunter | 500/day | 200/day | 100/day | 5000/hour |
| Cybersecurity | Unlimited | Unlimited | Unlimited | Unlimited |

## 🚨 Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request |
| 401 | Unauthorized |
| 403 | Forbidden |
| 404 | Not Found |
| 429 | Rate Limit Exceeded |
| 500 | Internal Server Error |

## 📝 Examples

### cURL Examples

**Login:**
```bash
curl -X POST http://localhost:3001/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"identifier":"demo","password":"demo123"}'
```

**OSINT Search:**
```bash
curl -X POST http://localhost:3001/api/v1/osint/search \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"type":"email","query":"<EMAIL>"}'
```

**Vulnerability Scan:**
```bash
curl -X POST http://localhost:3001/api/v1/vulnerability/scan \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"target":"https://example.com","scanTypes":["sqli","xss"]}'
```

## 🔗 SDKs & Libraries

- **JavaScript/Node.js**: `@kodexguard/sdk-js`
- **Python**: `kodexguard-python`
- **PHP**: `kodexguard/php-sdk`
- **Go**: `github.com/kodexguard/go-sdk`

---

**Untuk informasi lebih lanjut, kunjungi [docs.kodexguard.com](https://docs.kodexguard.com)**
