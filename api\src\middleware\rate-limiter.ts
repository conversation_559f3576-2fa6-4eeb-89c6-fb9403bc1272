import { Context, Next } from 'hono';
import { RateLimiterMemory } from 'rate-limiter-flexible';

// Create rate limiters for different endpoints
const globalLimiter = new RateLimiterMemory({
  keyGenerator: (req: any) => req.ip || 'anonymous',
  points: 100, // Number of requests
  duration: 900, // Per 15 minutes
});

const authLimiter = new RateLimiterMemory({
  keyGenerator: (req: any) => req.ip || 'anonymous',
  points: 5, // Number of requests
  duration: 900, // Per 15 minutes
});

const scanLimiter = new RateLimiterMemory({
  keyGenerator: (req: any) => req.userId || req.ip || 'anonymous',
  points: 10, // Number of requests
  duration: 3600, // Per hour
});

const apiLimiter = new RateLimiterMemory({
  keyGenerator: (req: any) => req.apiKey || req.userId || req.ip || 'anonymous',
  points: 1000, // Number of requests
  duration: 3600, // Per hour
});

export async function rateLimiter(c: Context, next: Next) {
  try {
    const ip = c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown';
    const path = c.req.path;
    const user = c.get('user');
    const apiKey = c.get('apiKey');

    let limiter = globalLimiter;
    let key = ip;

    // Choose appropriate limiter based on endpoint
    if (path.includes('/auth/')) {
      limiter = authLimiter;
    } else if (path.includes('/osint/') || path.includes('/vulnerability/') || path.includes('/file-analysis/')) {
      limiter = scanLimiter;
      key = user?.id || apiKey || ip;
    } else if (apiKey) {
      limiter = apiLimiter;
      key = apiKey;
    } else if (user) {
      key = user.id;
    }

    // Apply rate limiting
    const resRateLimiter = await limiter.consume(key);

    // Set rate limit headers
    c.header('X-RateLimit-Limit', limiter.points.toString());
    c.header('X-RateLimit-Remaining', resRateLimiter.remainingPoints?.toString() || '0');
    c.header('X-RateLimit-Reset', new Date(Date.now() + resRateLimiter.msBeforeNext).toISOString());

    await next();
  } catch (rejRes: any) {
    // Rate limit exceeded
    const secs = Math.round(rejRes.msBeforeNext / 1000) || 1;
    
    c.header('Retry-After', secs.toString());
    c.header('X-RateLimit-Limit', rejRes.totalHits?.toString() || '0');
    c.header('X-RateLimit-Remaining', '0');
    c.header('X-RateLimit-Reset', new Date(Date.now() + rejRes.msBeforeNext).toISOString());

    return c.json({
      success: false,
      error: 'Rate limit exceeded',
      message: `Too many requests. Try again in ${secs} seconds.`,
      retryAfter: secs
    }, 429);
  }
}

// Plan-based rate limiting
export function planRateLimiter(limits: { [plan: string]: number }) {
  return async (c: Context, next: Next) => {
    const user = c.get('user');
    
    if (!user) {
      await next();
      return;
    }

    const userLimit = limits[user.plan] || limits['free'] || 10;
    
    if (userLimit === -1) {
      // Unlimited for this plan
      await next();
      return;
    }

    const limiter = new RateLimiterMemory({
      keyGenerator: () => user.id,
      points: userLimit,
      duration: 3600, // Per hour
    });

    try {
      const resRateLimiter = await limiter.consume(user.id);
      
      c.header('X-Plan-Limit', userLimit.toString());
      c.header('X-Plan-Remaining', resRateLimiter.remainingPoints?.toString() || '0');
      c.header('X-Plan-Reset', new Date(Date.now() + resRateLimiter.msBeforeNext).toISOString());

      await next();
    } catch (rejRes: any) {
      const secs = Math.round(rejRes.msBeforeNext / 1000) || 1;
      
      return c.json({
        success: false,
        error: 'Plan limit exceeded',
        message: `You have reached your plan limit of ${userLimit} requests per hour. Upgrade your plan for higher limits.`,
        currentPlan: user.plan,
        limit: userLimit,
        retryAfter: secs
      }, 429);
    }
  };
}
