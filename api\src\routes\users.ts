import { <PERSON>o } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import { db } from '../database/connection';
import { users, apiKeys } from '../database/schema';
import { eq, and } from 'drizzle-orm';
import { generateApiKey, generateId } from '@kodexguard/shared';
import { asyncHandler, validationError, notFoundError } from '../middleware/error-handler';

const userRoutes = new Hono();

// Get current user profile
userRoutes.get('/profile', asyncHandler(async (c) => {
  const user = c.get('user');
  
  const [userProfile] = await db
    .select({
      id: users.id,
      username: users.username,
      email: users.email,
      fullName: users.fullName,
      avatar: users.avatar,
      bio: users.bio,
      role: users.role,
      plan: users.plan,
      planExpiry: users.planExpiry,
      createdAt: users.createdAt
    })
    .from(users)
    .where(eq(users.id, user.id))
    .limit(1);

  if (!userProfile) {
    throw notFoundError('User profile');
  }

  return c.json({
    success: true,
    data: userProfile
  });
}));

// Update user profile
const updateProfileSchema = z.object({
  fullName: z.string().min(2).max(100).optional(),
  bio: z.string().max(500).optional(),
  avatar: z.string().url().optional()
});

userRoutes.put('/profile', zValidator('json', updateProfileSchema), asyncHandler(async (c) => {
  const user = c.get('user');
  const updateData = c.req.valid('json');

  await db
    .update(users)
    .set({
      ...updateData,
      updatedAt: new Date()
    })
    .where(eq(users.id, user.id));

  return c.json({
    success: true,
    message: 'Profile updated successfully'
  });
}));

// Get user API keys
userRoutes.get('/api-keys', asyncHandler(async (c) => {
  const user = c.get('user');
  
  const userApiKeys = await db
    .select({
      id: apiKeys.id,
      name: apiKeys.name,
      key: apiKeys.key,
      isActive: apiKeys.isActive,
      lastUsed: apiKeys.lastUsed,
      usageCount: apiKeys.usageCount,
      rateLimit: apiKeys.rateLimit,
      createdAt: apiKeys.createdAt,
      expiresAt: apiKeys.expiresAt
    })
    .from(apiKeys)
    .where(eq(apiKeys.userId, user.id));

  // Mask API keys for security
  const maskedKeys = userApiKeys.map(key => ({
    ...key,
    key: key.key.substring(0, 8) + '...' + key.key.substring(key.key.length - 8)
  }));

  return c.json({
    success: true,
    data: maskedKeys
  });
}));

// Create new API key
const createApiKeySchema = z.object({
  name: z.string().min(1).max(100),
  rateLimit: z.number().min(1).max(10000).optional(),
  expiresAt: z.string().datetime().optional()
});

userRoutes.post('/api-keys', zValidator('json', createApiKeySchema), asyncHandler(async (c) => {
  const user = c.get('user');
  const { name, rateLimit = 100, expiresAt } = c.req.valid('json');

  // Check if user already has maximum API keys (limit to 5 per user)
  const existingKeys = await db
    .select({ count: apiKeys.id })
    .from(apiKeys)
    .where(and(
      eq(apiKeys.userId, user.id),
      eq(apiKeys.isActive, true)
    ));

  if (existingKeys.length >= 5) {
    throw validationError('Maximum API keys limit reached (5 keys per user)');
  }

  const newApiKey = generateApiKey();
  const apiKeyId = generateId();

  await db.insert(apiKeys).values({
    id: apiKeyId,
    userId: user.id,
    name,
    key: newApiKey,
    isActive: true,
    rateLimit,
    expiresAt: expiresAt ? new Date(expiresAt) : null
  });

  return c.json({
    success: true,
    message: 'API key created successfully',
    data: {
      id: apiKeyId,
      name,
      key: newApiKey, // Show full key only once
      rateLimit,
      expiresAt
    }
  }, 201);
}));

// Update API key
const updateApiKeySchema = z.object({
  name: z.string().min(1).max(100).optional(),
  isActive: z.boolean().optional(),
  rateLimit: z.number().min(1).max(10000).optional()
});

userRoutes.put('/api-keys/:id', zValidator('json', updateApiKeySchema), asyncHandler(async (c) => {
  const user = c.get('user');
  const apiKeyId = c.req.param('id');
  const updateData = c.req.valid('json');

  // Check if API key belongs to user
  const [existingKey] = await db
    .select({ id: apiKeys.id })
    .from(apiKeys)
    .where(and(
      eq(apiKeys.id, apiKeyId),
      eq(apiKeys.userId, user.id)
    ))
    .limit(1);

  if (!existingKey) {
    throw notFoundError('API key');
  }

  await db
    .update(apiKeys)
    .set(updateData)
    .where(eq(apiKeys.id, apiKeyId));

  return c.json({
    success: true,
    message: 'API key updated successfully'
  });
}));

// Delete API key
userRoutes.delete('/api-keys/:id', asyncHandler(async (c) => {
  const user = c.get('user');
  const apiKeyId = c.req.param('id');

  // Check if API key belongs to user
  const [existingKey] = await db
    .select({ id: apiKeys.id })
    .from(apiKeys)
    .where(and(
      eq(apiKeys.id, apiKeyId),
      eq(apiKeys.userId, user.id)
    ))
    .limit(1);

  if (!existingKey) {
    throw notFoundError('API key');
  }

  await db
    .delete(apiKeys)
    .where(eq(apiKeys.id, apiKeyId));

  return c.json({
    success: true,
    message: 'API key deleted successfully'
  });
}));

// Get user statistics
userRoutes.get('/stats', asyncHandler(async (c) => {
  const user = c.get('user');

  // Get usage statistics (this would be more complex in real implementation)
  const stats = {
    totalScans: 0,
    totalVulnerabilities: 0,
    totalFiles: 0,
    apiCalls: 0,
    planUsage: {
      osintQueries: 0,
      vulnScans: 0,
      fileAnalysis: 0,
      apiCalls: 0
    }
  };

  return c.json({
    success: true,
    data: stats
  });
}));

export default userRoutes;
