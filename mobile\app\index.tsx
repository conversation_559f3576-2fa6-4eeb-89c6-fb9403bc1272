import { useEffect } from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import { router } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withTiming, 
  withRepeat, 
  withSequence 
} from 'react-native-reanimated';
import { useAuth } from '../lib/auth-context';
import { colors, typography } from '../constants/theme';

const { width, height } = Dimensions.get('window');

export default function SplashScreen() {
  const { user, loading } = useAuth();
  const opacity = useSharedValue(0);
  const scale = useSharedValue(0.8);
  const glowOpacity = useSharedValue(0);

  useEffect(() => {
    // Start animations
    opacity.value = withTiming(1, { duration: 1000 });
    scale.value = withTiming(1, { duration: 1000 });
    glowOpacity.value = withRepeat(
      withSequence(
        withTiming(1, { duration: 1500 }),
        withTiming(0.3, { duration: 1500 })
      ),
      -1,
      true
    );

    // Navigate after loading
    const timer = setTimeout(() => {
      if (!loading) {
        if (user) {
          router.replace('/(tabs)');
        } else {
          router.replace('/auth');
        }
      }
    }, 2000);

    return () => clearTimeout(timer);
  }, [user, loading]);

  const logoStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
    transform: [{ scale: scale.value }],
  }));

  const glowStyle = useAnimatedStyle(() => ({
    opacity: glowOpacity.value,
  }));

  return (
    <LinearGradient
      colors={[colors.dark[950], colors.dark[900], colors.dark[800]]}
      style={styles.container}
    >
      {/* Background Grid */}
      <View style={styles.gridOverlay} />
      
      {/* Logo Container */}
      <Animated.View style={[styles.logoContainer, logoStyle]}>
        {/* Glow Effect */}
        <Animated.View style={[styles.glow, glowStyle]} />
        
        {/* Logo */}
        <View style={styles.logo}>
          <Text style={styles.logoIcon}>🛡️</Text>
        </View>
        
        {/* App Name */}
        <Text style={styles.appName}>KodeXGuard</Text>
        <Text style={styles.tagline}>Cybersecurity & Bug Hunting</Text>
      </Animated.View>

      {/* Loading Indicator */}
      <View style={styles.loadingContainer}>
        <View style={styles.loadingBar}>
          <Animated.View style={[styles.loadingProgress, glowStyle]} />
        </View>
        <Text style={styles.loadingText}>Initializing security modules...</Text>
      </View>

      {/* Version */}
      <Text style={styles.version}>v1.0.0</Text>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  gridOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    opacity: 0.1,
    backgroundColor: 'transparent',
    // Add grid pattern here if needed
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 80,
  },
  glow: {
    position: 'absolute',
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: colors.neon.blue,
    shadowColor: colors.neon.blue,
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.8,
    shadowRadius: 30,
    elevation: 20,
  },
  logo: {
    width: 80,
    height: 80,
    borderRadius: 20,
    backgroundColor: colors.dark[800],
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: colors.neon.blue,
    marginBottom: 20,
  },
  logoIcon: {
    fontSize: 40,
  },
  appName: {
    fontSize: 32,
    fontFamily: typography.fonts.bold,
    color: colors.white,
    marginBottom: 8,
    textAlign: 'center',
  },
  tagline: {
    fontSize: 16,
    fontFamily: typography.fonts.medium,
    color: colors.gray[400],
    textAlign: 'center',
  },
  loadingContainer: {
    position: 'absolute',
    bottom: 120,
    left: 40,
    right: 40,
    alignItems: 'center',
  },
  loadingBar: {
    width: '100%',
    height: 4,
    backgroundColor: colors.dark[700],
    borderRadius: 2,
    overflow: 'hidden',
    marginBottom: 16,
  },
  loadingProgress: {
    height: '100%',
    width: '70%',
    backgroundColor: colors.neon.blue,
    borderRadius: 2,
  },
  loadingText: {
    fontSize: 14,
    fontFamily: typography.fonts.regular,
    color: colors.gray[400],
    textAlign: 'center',
  },
  version: {
    position: 'absolute',
    bottom: 40,
    fontSize: 12,
    fontFamily: typography.fonts.regular,
    color: colors.gray[500],
  },
});
