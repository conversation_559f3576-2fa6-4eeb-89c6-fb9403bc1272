# KodeXGuard Production Environment Configuration
# Copy this file to .env.production and update with your production values

# Database Configuration
DB_HOST=mysql
DB_PORT=3306
DB_USER=kodexguard
DB_PASSWORD=your_secure_database_password
DB_NAME=kodexguard
DB_ROOT_PASSWORD=your_secure_root_password

# Redis Configuration
REDIS_URL=redis://redis:6379

# JWT Configuration
JWT_SECRET=your_super_secure_jwt_secret_key_here_minimum_32_characters

# Server Configuration
NODE_ENV=production
PORT=3001
API_URL=https://api.yourdomain.com

# CORS Configuration
CORS_ORIGIN=https://yourdomain.com,https://www.yourdomain.com

# Rate Limiting
RATE_LIMIT_MAX_REQUESTS=1000
RATE_LIMIT_WINDOW_MS=900000

# File Upload Configuration
MAX_FILE_SIZE=52428800
ALLOWED_FILE_TYPES=text/plain,application/javascript,application/php,application/python,text/x-python,application/zip

# SSL Configuration (if using HTTPS)
HTTPS_ENABLED=true
SSL_KEY_PATH=/etc/nginx/ssl/private.key
SSL_CERT_PATH=/etc/nginx/ssl/certificate.crt

# Bot Configuration
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
WHATSAPP_SESSION_PATH=/app/sessions/whatsapp

# Payment Gateway Configuration
# Tripay
TRIPAY_API_KEY=your_tripay_api_key
TRIPAY_PRIVATE_KEY=your_tripay_private_key
TRIPAY_MERCHANT_CODE=your_tripay_merchant_code
TRIPAY_CALLBACK_URL=https://api.yourdomain.com/api/v1/payments/callback/tripay

# Midtrans
MIDTRANS_SERVER_KEY=your_midtrans_server_key
MIDTRANS_CLIENT_KEY=your_midtrans_client_key
MIDTRANS_IS_PRODUCTION=true
MIDTRANS_CALLBACK_URL=https://api.yourdomain.com/api/v1/payments/callback/midtrans

# Xendit
XENDIT_SECRET_KEY=your_xendit_secret_key
XENDIT_CALLBACK_TOKEN=your_xendit_callback_token
XENDIT_CALLBACK_URL=https://api.yourdomain.com/api/v1/payments/callback/xendit

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_FROM=<EMAIL>

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=/app/logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# Security Configuration
BCRYPT_ROUNDS=12
SESSION_SECRET=your_session_secret_key
COOKIE_SECURE=true
COOKIE_SAME_SITE=strict

# Monitoring Configuration
SENTRY_DSN=your_sentry_dsn
ANALYTICS_ID=your_analytics_id

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=your_backup_bucket
BACKUP_S3_REGION=us-east-1
BACKUP_S3_ACCESS_KEY=your_s3_access_key
BACKUP_S3_SECRET_KEY=your_s3_secret_key

# CDN Configuration
CDN_URL=https://cdn.yourdomain.com
STATIC_URL=https://static.yourdomain.com

# External API Configuration
OSINT_API_TIMEOUT=30000
VULN_SCAN_TIMEOUT=300000
FILE_ANALYSIS_TIMEOUT=60000

# Feature Flags
FEATURE_OSINT_ENABLED=true
FEATURE_VULN_SCAN_ENABLED=true
FEATURE_FILE_ANALYSIS_ENABLED=true
FEATURE_BOT_ENABLED=true
FEATURE_PAYMENTS_ENABLED=true
FEATURE_LEADERBOARD_ENABLED=true

# Performance Configuration
MAX_CONCURRENT_SCANS=10
MAX_CONCURRENT_OSINT=20
MAX_CONCURRENT_FILE_ANALYSIS=5

# Cache Configuration
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# Domain Configuration
DOMAIN=yourdomain.com
API_DOMAIN=api.yourdomain.com
CDN_DOMAIN=cdn.yourdomain.com

# Next.js Configuration
NEXT_PUBLIC_API_URL=https://api.yourdomain.com
NEXT_PUBLIC_WS_URL=wss://api.yourdomain.com
NEXT_PUBLIC_APP_NAME=KodeXGuard
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_DOMAIN=yourdomain.com

# Docker Configuration
COMPOSE_PROJECT_NAME=kodexguard
COMPOSE_FILE=docker-compose.yml:docker-compose.prod.yml

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30s
HEALTH_CHECK_TIMEOUT=10s
HEALTH_CHECK_RETRIES=3

# Maintenance Mode
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE=System sedang dalam maintenance. Silakan coba lagi nanti.

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your_secure_admin_password

# API Documentation
API_DOCS_ENABLED=true
API_DOCS_PATH=/docs

# Webhook Configuration
WEBHOOK_SECRET=your_webhook_secret
WEBHOOK_TIMEOUT=10000

# Social Media Configuration
DISCORD_WEBHOOK_URL=your_discord_webhook_url
TELEGRAM_CHANNEL_ID=your_telegram_channel_id
TWITTER_API_KEY=your_twitter_api_key
TWITTER_API_SECRET=your_twitter_api_secret

# Analytics Configuration
GOOGLE_ANALYTICS_ID=your_google_analytics_id
FACEBOOK_PIXEL_ID=your_facebook_pixel_id

# SEO Configuration
SITE_NAME=KodeXGuard
SITE_DESCRIPTION=Platform mandiri cybersecurity & bug hunting Indonesia
SITE_KEYWORDS=cybersecurity,bug hunting,osint,vulnerability scanner,indonesia
SITE_AUTHOR=KodeXGuard Team

# Legal Configuration
PRIVACY_POLICY_URL=https://yourdomain.com/privacy
TERMS_OF_SERVICE_URL=https://yourdomain.com/terms
CONTACT_EMAIL=<EMAIL>
SUPPORT_EMAIL=<EMAIL>
