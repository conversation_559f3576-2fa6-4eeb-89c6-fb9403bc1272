import { Hono } from 'hono';
import { sign } from 'hono/jwt';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import bcrypt from 'bcryptjs';
import { db } from '../database/connection';
import { users } from '../database/schema';
import { eq, and } from 'drizzle-orm';
import { generateId, validateEmail, validateUsername, validatePassword } from '@kodexguard/shared';
import { asyncHandler, validationError, unauthorizedError, createError } from '../middleware/error-handler';

const auth = new Hono();

// Validation schemas
const registerSchema = z.object({
  username: z.string().min(3).max(30),
  email: z.string().email(),
  password: z.string().min(8),
  fullName: z.string().min(2).max(100)
});

const loginSchema = z.object({
  identifier: z.string(), // username or email
  password: z.string()
});

const forgotPasswordSchema = z.object({
  email: z.string().email()
});

const resetPasswordSchema = z.object({
  token: z.string(),
  password: z.string().min(8)
});

// Register endpoint
auth.post('/register', zValidator('json', registerSchema), asyncHandler(async (c) => {
  const { username, email, password, fullName } = c.req.valid('json');

  // Validate input
  if (!validateUsername(username)) {
    throw validationError('Invalid username format');
  }

  if (!validateEmail(email)) {
    throw validationError('Invalid email format');
  }

  if (!validatePassword(password)) {
    throw validationError('Password must contain at least 8 characters with uppercase, lowercase, and number');
  }

  // Check if user already exists
  const [existingUser] = await db
    .select({ id: users.id })
    .from(users)
    .where(
      and(
        eq(users.username, username),
        eq(users.email, email)
      )
    )
    .limit(1);

  if (existingUser) {
    throw createError('Username or email already exists', 409, 'CONFLICT');
  }

  // Hash password
  const hashedPassword = await bcrypt.hash(password, 12);

  // Create user
  const userId = generateId();
  await db.insert(users).values({
    id: userId,
    username,
    email,
    password: hashedPassword,
    fullName,
    role: 'user',
    plan: 'free',
    isActive: true,
    emailVerified: false
  });

  // Generate JWT token
  const token = await sign({
    userId,
    username,
    email,
    role: 'user',
    plan: 'free',
    exp: Math.floor(Date.now() / 1000) + (7 * 24 * 60 * 60) // 7 days
  }, process.env.JWT_SECRET!);

  return c.json({
    success: true,
    message: 'User registered successfully',
    data: {
      user: {
        id: userId,
        username,
        email,
        fullName,
        role: 'user',
        plan: 'free'
      },
      token
    }
  }, 201);
}));

// Login endpoint
auth.post('/login', zValidator('json', loginSchema), asyncHandler(async (c) => {
  const { identifier, password } = c.req.valid('json');

  // Find user by username or email
  const [user] = await db
    .select()
    .from(users)
    .where(
      and(
        eq(users.username, identifier),
        eq(users.email, identifier)
      )
    )
    .limit(1);

  if (!user) {
    throw unauthorizedError('Invalid credentials');
  }

  if (!user.isActive) {
    throw unauthorizedError('Account is deactivated');
  }

  // Verify password
  const isValidPassword = await bcrypt.compare(password, user.password);
  if (!isValidPassword) {
    throw unauthorizedError('Invalid credentials');
  }

  // Update last login
  await db
    .update(users)
    .set({ lastLogin: new Date() })
    .where(eq(users.id, user.id));

  // Generate JWT token
  const token = await sign({
    userId: user.id,
    username: user.username,
    email: user.email,
    role: user.role,
    plan: user.plan,
    exp: Math.floor(Date.now() / 1000) + (7 * 24 * 60 * 60) // 7 days
  }, process.env.JWT_SECRET!);

  return c.json({
    success: true,
    message: 'Login successful',
    data: {
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        fullName: user.fullName,
        avatar: user.avatar,
        role: user.role,
        plan: user.plan,
        planExpiry: user.planExpiry
      },
      token
    }
  });
}));

// Forgot password endpoint
auth.post('/forgot-password', zValidator('json', forgotPasswordSchema), asyncHandler(async (c) => {
  const { email } = c.req.valid('json');

  // Check if user exists
  const [user] = await db
    .select({ id: users.id, email: users.email, fullName: users.fullName })
    .from(users)
    .where(eq(users.email, email))
    .limit(1);

  if (!user) {
    // Don't reveal if email exists or not
    return c.json({
      success: true,
      message: 'If the email exists, a reset link has been sent'
    });
  }

  // Generate reset token (in production, store this in database with expiry)
  const resetToken = await sign({
    userId: user.id,
    type: 'password_reset',
    exp: Math.floor(Date.now() / 1000) + (60 * 60) // 1 hour
  }, process.env.JWT_SECRET!);

  // TODO: Send email with reset link
  console.log(`Password reset token for ${email}: ${resetToken}`);

  return c.json({
    success: true,
    message: 'If the email exists, a reset link has been sent'
  });
}));

// Reset password endpoint
auth.post('/reset-password', zValidator('json', resetPasswordSchema), asyncHandler(async (c) => {
  const { token, password } = c.req.valid('json');

  if (!validatePassword(password)) {
    throw validationError('Password must contain at least 8 characters with uppercase, lowercase, and number');
  }

  try {
    // Verify reset token
    const payload = await verify(token, process.env.JWT_SECRET!);
    
    if (payload.type !== 'password_reset') {
      throw unauthorizedError('Invalid reset token');
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Update user password
    await db
      .update(users)
      .set({ password: hashedPassword })
      .where(eq(users.id, payload.userId as string));

    return c.json({
      success: true,
      message: 'Password reset successfully'
    });
  } catch (error) {
    throw unauthorizedError('Invalid or expired reset token');
  }
}));

// Verify token endpoint
auth.get('/verify', asyncHandler(async (c) => {
  const authHeader = c.req.header('Authorization');
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw unauthorizedError('Token required');
  }

  const token = authHeader.substring(7);

  try {
    const payload = await verify(token, process.env.JWT_SECRET!);
    
    // Get fresh user data
    const [user] = await db
      .select({
        id: users.id,
        username: users.username,
        email: users.email,
        fullName: users.fullName,
        avatar: users.avatar,
        role: users.role,
        plan: users.plan,
        planExpiry: users.planExpiry,
        isActive: users.isActive
      })
      .from(users)
      .where(eq(users.id, payload.userId as string))
      .limit(1);

    if (!user || !user.isActive) {
      throw unauthorizedError('User not found or inactive');
    }

    return c.json({
      success: true,
      data: {
        user,
        tokenValid: true
      }
    });
  } catch (error) {
    throw unauthorizedError('Invalid or expired token');
  }
}));

export default auth;
