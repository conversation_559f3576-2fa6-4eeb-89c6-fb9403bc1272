'use client';

import { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import {
  MagnifyingGlassIcon,
  ShieldCheckIcon,
  DocumentMagnifyingGlassIcon,
  BugAntIcon,
  ChartBarIcon,
  ClockIcon,
  TrophyIcon,
  FireIcon,
} from '@heroicons/react/24/outline';
import { useAuth } from '@/lib/auth-context';
import { userAPI, leaderboardAPI, cveAPI } from '@/lib/api';
import { useQuery } from 'react-query';
import DashboardLayout from '@/components/layout/dashboard-layout';

interface DashboardStats {
  totalScans: number;
  totalVulnerabilities: number;
  totalFiles: number;
  apiCalls: number;
  planUsage: {
    osintQueries: number;
    vulnScans: number;
    fileAnalysis: number;
    apiCalls: number;
  };
}

export default function DashboardPage() {
  const { user } = useAuth();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Fetch user stats
  const { data: stats, isLoading: statsLoading } = useQuery<DashboardStats>(
    'user-stats',
    () => userAPI.getStats().then(res => res.data.data),
    { enabled: mounted }
  );

  // Fetch leaderboard position
  const { data: leaderboardPosition } = useQuery(
    'leaderboard-position',
    () => leaderboardAPI.getMyPosition().then(res => res.data.data),
    { enabled: mounted }
  );

  // Fetch recent CVEs
  const { data: recentCVEs } = useQuery(
    'recent-cves',
    () => cveAPI.getRecent(5).then(res => res.data.data),
    { enabled: mounted }
  );

  if (!mounted) {
    return null;
  }

  const quickActions = [
    {
      name: 'OSINT Search',
      description: 'Mulai investigasi OSINT',
      href: '/dashboard/osint',
      icon: MagnifyingGlassIcon,
      color: 'text-neon-blue',
      bgColor: 'bg-neon-blue/10',
    },
    {
      name: 'Vulnerability Scan',
      description: 'Scan kerentanan website',
      href: '/dashboard/vulnerability',
      icon: ShieldCheckIcon,
      color: 'text-neon-purple',
      bgColor: 'bg-neon-purple/10',
    },
    {
      name: 'File Analysis',
      description: 'Analisis file mencurigakan',
      href: '/dashboard/file-analysis',
      icon: DocumentMagnifyingGlassIcon,
      color: 'text-neon-green',
      bgColor: 'bg-neon-green/10',
    },
    {
      name: 'CVE Database',
      description: 'Cari CVE terbaru',
      href: '/dashboard/cve',
      icon: BugAntIcon,
      color: 'text-red-400',
      bgColor: 'bg-red-400/10',
    },
  ];

  const statCards = [
    {
      name: 'Total Scans',
      value: stats?.totalScans || 0,
      icon: MagnifyingGlassIcon,
      color: 'text-neon-blue',
      bgColor: 'bg-neon-blue/10',
    },
    {
      name: 'Vulnerabilities Found',
      value: stats?.totalVulnerabilities || 0,
      icon: BugAntIcon,
      color: 'text-red-400',
      bgColor: 'bg-red-400/10',
    },
    {
      name: 'Files Analyzed',
      value: stats?.totalFiles || 0,
      icon: DocumentMagnifyingGlassIcon,
      color: 'text-neon-green',
      bgColor: 'bg-neon-green/10',
    },
    {
      name: 'API Calls',
      value: stats?.apiCalls || 0,
      icon: ChartBarIcon,
      color: 'text-neon-purple',
      bgColor: 'bg-neon-purple/10',
    },
  ];

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Welcome Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-white mb-2">
              Selamat datang kembali, {user?.fullName}! 👋
            </h1>
            <p className="text-gray-400">
              Lanjutkan perjalanan cybersecurity Anda hari ini
            </p>
          </div>
        </motion.div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {statCards.map((stat, index) => (
            <motion.div
              key={stat.name}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="card-cyber p-6"
            >
              <div className="flex items-center">
                <div className={`w-12 h-12 ${stat.bgColor} rounded-lg flex items-center justify-center`}>
                  <stat.icon className={`w-6 h-6 ${stat.color}`} />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-400">{stat.name}</p>
                  <p className="text-2xl font-bold text-white">
                    {statsLoading ? '...' : stat.value.toLocaleString()}
                  </p>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <h2 className="text-xl font-semibold text-white mb-4">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {quickActions.map((action, index) => (
              <motion.a
                key={action.name}
                href={action.href}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.5 + index * 0.1 }}
                className="card-cyber-glow p-6 hover-lift block"
              >
                <div className={`w-12 h-12 ${action.bgColor} rounded-lg flex items-center justify-center mb-4`}>
                  <action.icon className={`w-6 h-6 ${action.color}`} />
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">{action.name}</h3>
                <p className="text-gray-400 text-sm">{action.description}</p>
              </motion.a>
            ))}
          </div>
        </motion.div>

        {/* Dashboard Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Plan Status */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            className="card-cyber p-6"
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">Plan Status</h3>
              <span className={`px-3 py-1 rounded-full text-sm plan-${user?.plan}`}>
                {user?.plan?.toUpperCase()}
              </span>
            </div>
            
            <div className="space-y-4">
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span className="text-gray-400">OSINT Queries</span>
                  <span className="text-white">{stats?.planUsage.osintQueries || 0}/50</span>
                </div>
                <div className="w-full bg-dark-700 rounded-full h-2">
                  <div 
                    className="bg-neon-blue h-2 rounded-full" 
                    style={{ width: `${Math.min(((stats?.planUsage.osintQueries || 0) / 50) * 100, 100)}%` }}
                  />
                </div>
              </div>
              
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span className="text-gray-400">Vulnerability Scans</span>
                  <span className="text-white">{stats?.planUsage.vulnScans || 0}/25</span>
                </div>
                <div className="w-full bg-dark-700 rounded-full h-2">
                  <div 
                    className="bg-neon-purple h-2 rounded-full" 
                    style={{ width: `${Math.min(((stats?.planUsage.vulnScans || 0) / 25) * 100, 100)}%` }}
                  />
                </div>
              </div>
              
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span className="text-gray-400">File Analysis</span>
                  <span className="text-white">{stats?.planUsage.fileAnalysis || 0}/15</span>
                </div>
                <div className="w-full bg-dark-700 rounded-full h-2">
                  <div 
                    className="bg-neon-green h-2 rounded-full" 
                    style={{ width: `${Math.min(((stats?.planUsage.fileAnalysis || 0) / 15) * 100, 100)}%` }}
                  />
                </div>
              </div>
            </div>
            
            <div className="mt-4 pt-4 border-t border-dark-600">
              <a 
                href="/dashboard/plans" 
                className="text-neon-blue hover:text-neon-blue/80 text-sm font-medium"
              >
                Upgrade Plan →
              </a>
            </div>
          </motion.div>

          {/* Leaderboard Position */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.9 }}
            className="card-cyber p-6"
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">Leaderboard</h3>
              <TrophyIcon className="w-5 h-5 text-yellow-400" />
            </div>
            
            <div className="text-center">
              <div className="text-3xl font-bold text-white mb-2">
                #{leaderboardPosition?.rank || 'N/A'}
              </div>
              <p className="text-gray-400 mb-4">Peringkat Anda</p>
              
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-lg font-semibold text-white">
                    {leaderboardPosition?.score || 0}
                  </div>
                  <div className="text-xs text-gray-400">Score</div>
                </div>
                <div>
                  <div className="text-lg font-semibold text-white">
                    {leaderboardPosition?.totalScans || 0}
                  </div>
                  <div className="text-xs text-gray-400">Scans</div>
                </div>
                <div>
                  <div className="text-lg font-semibold text-white">
                    {leaderboardPosition?.totalVulns || 0}
                  </div>
                  <div className="text-xs text-gray-400">Vulns</div>
                </div>
              </div>
            </div>
            
            <div className="mt-4 pt-4 border-t border-dark-600">
              <a 
                href="/dashboard/leaderboard" 
                className="text-neon-blue hover:text-neon-blue/80 text-sm font-medium"
              >
                Lihat Leaderboard →
              </a>
            </div>
          </motion.div>
        </div>

        {/* Recent CVEs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1.0 }}
          className="card-cyber p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">CVE Terbaru</h3>
            <FireIcon className="w-5 h-5 text-red-400" />
          </div>
          
          <div className="space-y-3">
            {recentCVEs?.slice(0, 5).map((cve: any, index: number) => (
              <div key={cve.id} className="flex items-center justify-between p-3 bg-dark-800/50 rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <span className="font-mono text-sm text-neon-blue">{cve.cveId}</span>
                    <span className={`px-2 py-1 rounded text-xs severity-${cve.severity}`}>
                      {cve.severity?.toUpperCase()}
                    </span>
                  </div>
                  <p className="text-sm text-gray-400 mt-1 truncate">
                    {cve.description}
                  </p>
                </div>
                <div className="text-right">
                  <div className="text-sm font-semibold text-white">
                    {cve.cvssScore || 'N/A'}
                  </div>
                  <div className="text-xs text-gray-400">CVSS</div>
                </div>
              </div>
            ))}
          </div>
          
          <div className="mt-4 pt-4 border-t border-dark-600">
            <a 
              href="/dashboard/cve" 
              className="text-neon-blue hover:text-neon-blue/80 text-sm font-medium"
            >
              Lihat Semua CVE →
            </a>
          </div>
        </motion.div>
      </div>
    </DashboardLayout>
  );
}
