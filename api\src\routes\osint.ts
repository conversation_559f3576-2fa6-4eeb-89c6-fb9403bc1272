import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import { db } from '../database/connection';
import { osintResults } from '../database/schema';
import { eq } from 'drizzle-orm';
import { generateId, OSINTType, validateNIK, validateNPWP, validatePhone, validateEmail } from '@kodexguard/shared';
import { asyncHandler, validationError } from '../middleware/error-handler';
import { planRateLimiter } from '../middleware/rate-limiter';

const osintRoutes = new Hono();

// Apply plan-based rate limiting
osintRoutes.use('*', planRateLimiter({
  free: 10,
  student: 50,
  hobby: 100,
  bughunter: 500,
  cybersecurity: -1
}));

// OSINT search schema
const osintSearchSchema = z.object({
  type: z.enum(['name', 'nik', 'npwp', 'phone', 'imei', 'email', 'domain']),
  query: z.string().min(1).max(255),
  options: z.object({
    deepSearch: z.boolean().optional(),
    includeLeaks: z.boolean().optional(),
    includeGithub: z.boolean().optional(),
    includeSocial: z.boolean().optional()
  }).optional()
});

// OSINT search endpoint
osintRoutes.post('/search', zValidator('json', osintSearchSchema), asyncHandler(async (c) => {
  const user = c.get('user');
  const { type, query, options = {} } = c.req.valid('json');

  // Validate query based on type
  switch (type) {
    case 'nik':
      if (!validateNIK(query)) {
        throw validationError('Invalid NIK format (must be 16 digits)');
      }
      break;
    case 'npwp':
      if (!validateNPWP(query)) {
        throw validationError('Invalid NPWP format (must be 15 digits)');
      }
      break;
    case 'phone':
      if (!validatePhone(query)) {
        throw validationError('Invalid phone number format');
      }
      break;
    case 'email':
      if (!validateEmail(query)) {
        throw validationError('Invalid email format');
      }
      break;
  }

  // Create OSINT result record
  const resultId = generateId();
  await db.insert(osintResults).values({
    id: resultId,
    userId: user.id,
    type,
    query,
    results: [],
    confidence: 0,
    sources: [],
    status: 'pending'
  });

  // Start OSINT search (async)
  performOSINTSearch(resultId, type as OSINTType, query, options);

  return c.json({
    success: true,
    message: 'OSINT search started',
    data: {
      searchId: resultId,
      type,
      query: maskSensitiveQuery(type, query),
      status: 'pending'
    }
  }, 202);
}));

// Get OSINT search result
osintRoutes.get('/search/:id', asyncHandler(async (c) => {
  const user = c.get('user');
  const searchId = c.req.param('id');

  const [result] = await db
    .select()
    .from(osintResults)
    .where(eq(osintResults.id, searchId))
    .limit(1);

  if (!result || result.userId !== user.id) {
    throw notFoundError('OSINT search result');
  }

  return c.json({
    success: true,
    data: {
      ...result,
      query: maskSensitiveQuery(result.type, result.query)
    }
  });
}));

// Get user's OSINT search history
osintRoutes.get('/history', asyncHandler(async (c) => {
  const user = c.get('user');
  const page = parseInt(c.req.query('page') || '1');
  const limit = parseInt(c.req.query('limit') || '20');
  const offset = (page - 1) * limit;

  const results = await db
    .select({
      id: osintResults.id,
      type: osintResults.type,
      query: osintResults.query,
      confidence: osintResults.confidence,
      status: osintResults.status,
      createdAt: osintResults.createdAt
    })
    .from(osintResults)
    .where(eq(osintResults.userId, user.id))
    .orderBy(osintResults.createdAt)
    .limit(limit)
    .offset(offset);

  // Mask sensitive queries
  const maskedResults = results.map(result => ({
    ...result,
    query: maskSensitiveQuery(result.type, result.query)
  }));

  return c.json({
    success: true,
    data: maskedResults,
    pagination: {
      page,
      limit,
      total: results.length
    }
  });
}));

// OSINT data sources endpoint
osintRoutes.get('/sources', asyncHandler(async (c) => {
  const sources = {
    name: ['dukcapil_leaked', 'kemkes_leaked', 'social_media', 'public_records'],
    nik: ['dukcapil_leaked', 'kemkes_leaked', 'bpjs_leaked'],
    npwp: ['tax_records', 'business_registry'],
    phone: ['truecaller', 'getcontact', 'social_media', 'leaked_databases'],
    imei: ['device_registry', 'stolen_device_db'],
    email: ['haveibeenpwned', 'github_commits', 'social_media', 'leaked_databases'],
    domain: ['whois', 'dns_records', 'subdomain_enum', 'certificate_transparency']
  };

  return c.json({
    success: true,
    data: sources
  });
}));

// Helper functions
function maskSensitiveQuery(type: string, query: string): string {
  switch (type) {
    case 'nik':
    case 'npwp':
      return query.substring(0, 4) + '*'.repeat(query.length - 8) + query.substring(query.length - 4);
    case 'phone':
      return query.substring(0, 4) + '*'.repeat(query.length - 6) + query.substring(query.length - 2);
    case 'email':
      const [local, domain] = query.split('@');
      return local.substring(0, 2) + '*'.repeat(local.length - 2) + '@' + domain;
    default:
      return query.length > 10 ? query.substring(0, 4) + '*'.repeat(query.length - 8) + query.substring(query.length - 4) : query;
  }
}

async function performOSINTSearch(resultId: string, type: OSINTType, query: string, options: any) {
  try {
    // Update status to running
    await db
      .update(osintResults)
      .set({ status: 'running' })
      .where(eq(osintResults.id, resultId));

    // Simulate OSINT search (replace with actual implementation)
    const searchResults = await simulateOSINTSearch(type, query, options);

    // Update with results
    await db
      .update(osintResults)
      .set({
        results: searchResults.results,
        confidence: searchResults.confidence,
        sources: searchResults.sources,
        status: 'completed'
      })
      .where(eq(osintResults.id, resultId));

  } catch (error) {
    console.error('OSINT search error:', error);
    
    // Update status to failed
    await db
      .update(osintResults)
      .set({ status: 'failed' })
      .where(eq(osintResults.id, resultId));
  }
}

async function simulateOSINTSearch(type: OSINTType, query: string, options: any) {
  // Simulate search delay
  await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 3000));

  // Mock results based on type
  const mockResults = {
    results: [
      {
        source: 'mock_database',
        data: {
          query,
          type,
          found: Math.random() > 0.5,
          details: `Mock result for ${type}: ${query}`
        },
        confidence: Math.random() * 100,
        verified: Math.random() > 0.3
      }
    ],
    confidence: Math.random() * 100,
    sources: ['mock_database', 'test_source']
  };

  return mockResults;
}

export default osintRoutes;
