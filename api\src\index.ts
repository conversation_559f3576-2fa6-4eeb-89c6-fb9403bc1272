import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { prettyJSON } from 'hono/pretty-json';
import { secureHeaders } from 'hono/secure-headers';
import { serve } from '@hono/node-server';
import 'dotenv/config';

import { testConnection } from './database/connection';
import { errorHandler } from './middleware/error-handler';
import { rateLimiter } from './middleware/rate-limiter';
import { authMiddleware } from './middleware/auth';

// Import routes
import authRoutes from './routes/auth';
import userRoutes from './routes/users';
import osintRoutes from './routes/osint';
import vulnRoutes from './routes/vulnerability';
import fileRoutes from './routes/file-analysis';
import botRoutes from './routes/bots';
import planRoutes from './routes/plans';
import adminRoutes from './routes/admin';
import toolsRoutes from './routes/tools';
import cveRoutes from './routes/cve';
import dorkRoutes from './routes/dorking';
import leaderboardRoutes from './routes/leaderboard';

const app = new Hono();

// Global middleware
app.use('*', logger());
app.use('*', prettyJSON());
app.use('*', secureHeaders());

// CORS configuration
app.use('*', cors({
  origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
  credentials: true,
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization', 'X-API-Key']
}));

// Rate limiting
app.use('*', rateLimiter);

// Health check endpoint
app.get('/health', async (c) => {
  const dbHealth = await testConnection();
  return c.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    database: dbHealth ? 'connected' : 'disconnected'
  });
});

// API routes
const apiRoutes = new Hono();

// Public routes (no auth required)
apiRoutes.route('/auth', authRoutes);
apiRoutes.route('/health', new Hono().get('/', (c) => c.json({ status: 'ok' })));

// Protected routes (auth required)
apiRoutes.use('/users/*', authMiddleware);
apiRoutes.use('/osint/*', authMiddleware);
apiRoutes.use('/vulnerability/*', authMiddleware);
apiRoutes.use('/file-analysis/*', authMiddleware);
apiRoutes.use('/bots/*', authMiddleware);
apiRoutes.use('/plans/*', authMiddleware);
apiRoutes.use('/tools/*', authMiddleware);
apiRoutes.use('/cve/*', authMiddleware);
apiRoutes.use('/dorking/*', authMiddleware);
apiRoutes.use('/leaderboard/*', authMiddleware);
apiRoutes.use('/admin/*', authMiddleware);

apiRoutes.route('/users', userRoutes);
apiRoutes.route('/osint', osintRoutes);
apiRoutes.route('/vulnerability', vulnRoutes);
apiRoutes.route('/file-analysis', fileRoutes);
apiRoutes.route('/bots', botRoutes);
apiRoutes.route('/plans', planRoutes);
apiRoutes.route('/tools', toolsRoutes);
apiRoutes.route('/cve', cveRoutes);
apiRoutes.route('/dorking', dorkRoutes);
apiRoutes.route('/leaderboard', leaderboardRoutes);
apiRoutes.route('/admin', adminRoutes);

// Mount API routes
app.route('/api/v1', apiRoutes);

// 404 handler
app.notFound((c) => {
  return c.json({
    success: false,
    error: 'Endpoint not found',
    message: 'The requested endpoint does not exist'
  }, 404);
});

// Error handler
app.onError(errorHandler);

// Root endpoint
app.get('/', (c) => {
  return c.json({
    name: 'KodeXGuard API',
    version: '1.0.0',
    description: 'Platform mandiri cybersecurity & bug hunting Indonesia',
    documentation: '/api/v1/docs',
    health: '/health',
    timestamp: new Date().toISOString()
  });
});

// Start server
const port = parseInt(process.env.PORT || '3001');

console.log(`🚀 KodeXGuard API starting on port ${port}`);

// Test database connection on startup
testConnection().then((connected) => {
  if (!connected) {
    console.error('❌ Failed to connect to database. Exiting...');
    process.exit(1);
  }
});

serve({
  fetch: app.fetch,
  port
}, (info) => {
  console.log(`✅ Server running at http://localhost:${info.port}`);
});

export default app;
