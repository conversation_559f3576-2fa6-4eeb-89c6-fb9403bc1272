import type { Config } from 'drizzle-kit';
import 'dotenv/config';

export default {
  schema: './src/database/schema.ts',
  out: './src/database/migrations',
  driver: 'mysql2',
  dbCredentials: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'kodexguard'
  },
  verbose: true,
  strict: true
} satisfies Config;
